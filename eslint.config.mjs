import perfectionist from 'eslint-plugin-perfectionist';
import eslintPluginImport from 'eslint-plugin-import';
import pluginNode from 'eslint-plugin-node';
import tseslint from 'typescript-eslint';
import pluginJs from '@eslint/js';
import globals from 'globals';

/** @type {import('eslint').Linter.Config[]} */
export default [
	// A dedicated, top-level ignores configuration.
	{
		ignores: [
			'dist/**',
			'docs/**',
			'logs/**',
			'types/**',
			'shared/**',
			'.eslintcache',
			'node_modules/**',
			'scripts/**'
		]
	},
	{ files: ['**/*.{js,mjs,cjs,ts}'] },
	{ languageOptions: { globals: globals.node } },
	pluginJs.configs.recommended,
	...tseslint.configs.recommended,
	{
		plugins: {
			import: eslintPluginImport,
			perfectionist,
			node: pluginNode
		},
		rules: {
			// Initial Rules
			'node/no-process-env': 'error',
			'no-mixed-spaces-and-tabs': 'error',
			'@typescript-eslint/no-explicit-any': 'warn',
			indent: ['error', 'tab', { SwitchCase: 1 }],
			'@typescript-eslint/no-require-imports': 'warn',
			'@typescript-eslint/no-empty-object-type': 'warn',
			'@typescript-eslint/no-unused-vars': [
				'warn',
				{
					vars: 'all', // Check all variables
					args: 'after-used', // Ignore unused arguments after the last used argument
					argsIgnorePattern: '^_', // Ignore variables prefixed with _
					ignoreRestSiblings: true // Ignore unused siblings in rest destructuring
				}
			],
			'no-warning-comments': [
				'warn',
				{
					terms: ['todo', 'fixme'],
					location: 'start'
				}
			],

			// --- CORRECTED 'sort-imports' RULE ---
			'perfectionist/sort-imports': [
				'error',
				{
					type: 'line-length',
					order: 'desc',
					groups: [['import']],
					newlinesBetween: 'always'
				}
			],
			// Sort the items inside the curly braces by length
			'perfectionist/sort-named-imports': [
				'error',
				{
					type: 'line-length',
					order: 'desc'
				}
			],

			// --- Keep these non-conflicting rules from eslint-plugin-import ---
			'import/order': 'off', // MUST be off to prevent conflicts
			'import/newline-after-import': ['error', { count: 1 }],
			'import/no-duplicates': 'error',
			'import/extensions': ['off', 'ignorePackages', { ts: 'never' }],

			// --- Other Quality Rules ---
			'prefer-const': 'error',
			'no-var': 'error',
			'no-console': 'off',
			'no-debugger': 'error',
			'no-unused-expressions': 'warn'
		},
		settings: {
			'import/resolver': {
				typescript: {
					// Tell the resolver where your tsconfig is
					project: './tsconfig.json',
					alwaysTryTypes: true // Resolve type definitions as well
				}
			}
		}
	}
];