import { cli_entry } from 'excelytics.shared-models';

/**
 * CLI entry point for excelytics.shared-models
 * commands:
 *  - map:enhanced
 *  - map
 *  - reinstall
 *  - clean:imports
 *  - config:test
 */
if (import.meta.main) {
	cli_entry().catch((err) => {
		console.error("<PERSON><PERSON><PERSON> failed to execute:", err);
		process.exit(1);
	});
}

/**
 * CLI dispatcher. The first argument determines which script to run.
 * Loads configuration once and passes it to individual functions.
 */
/*
async function main() {
	const [command, ...args] = process.argv.slice(2);

	// Load configuration once at the CLI level
	console.log('🔧 Loading configuration...');
	const configManager = ConfigManager.getInstance();
	const config = await configManager.getConfig();

	switch (command) {
		// ! Map the file structure (with metrics)
		case "map:enhanced": {
			const rootArg = args.find((arg) => !arg.startsWith("--"));

			// Use configured rootDirectory if no path argument provided
			let rootPath: string;
			if (!rootArg) {
				if (config.rootDirectory) {
					rootPath = path.resolve(config.rootDirectory);
					console.log(`📁 Using configured root directory: ${rootPath}`);
				} else {
					console.error("❌ Error: 'map:enhanced' command requires a path or configured rootDirectory.");
					console.log("Usage: bun script map:enhanced [<path>] [--folders-only]");
					console.log("Tip: Set 'rootDirectory' in .excelyticsrc.json to use without path argument");
					process.exit(1);
				}
			} else {
				rootPath = path.resolve(rootArg);
				console.log(`📁 Using provided path: ${rootPath}`);
			}

			const options = {
				foldersOnly: args.includes("--folders-only"),
				showAll: args.includes("--show-all"),
				showAllWithHideList: args.includes("--show-all-with-hide-list"),
			};

			// Use the enhanced helper with pre-loaded config
			await FileStructureHelper.generateEnhancedFileStructure(rootPath, options, config);
			break;
		}
		//! Map the file structure (using pre-loaded config)
		case "map": {
			const rootArg = args.find((arg) => !arg.startsWith("--"));

			// Use configured rootDirectory if no path argument provided
			let rootPath: string;
			if (!rootArg) {
				if (config.rootDirectory) {
					rootPath = path.resolve(config.rootDirectory);
					console.log(`📁 Using configured root directory: ${rootPath}`);
				} else {
					console.error("❌ Error: 'map' command requires a path or configured rootDirectory.");
					console.log("Usage: bun script map [<path>] [--folders-only]");
					console.log("Tip: Set 'rootDirectory' in .excelyticsrc.json to use without path argument");
					process.exit(1);
				}
			} else {
				rootPath = path.resolve(rootArg);
				console.log(`📁 Using provided path: ${rootPath}`);
			}

			const options = {
				foldersOnly: args.includes("--folders-only"),
				showAll: args.includes("--show-all"),
				showAllWithHideList: args.includes("--show-all-with-hide-list"),
			};

			// Use the helper with pre-loaded config
			await FileStructureHelper.generateSimpleFileStructure(rootPath, options, config);
			break;
		}
		// ! Reinstall dependencies
		case "reinstall": {
			await reinstallDependencies();
			break;
		}
		// ! Clean up import statements
		case "clean:imports": {
			// For now, it runs with defaults. You could extend this to parse directory arguments from the command line.
			await cleanupImports();
			break;
		}

		// ! Test configuration loading
		case "config:test": {
			console.log('🧪 Testing configuration system...\n');

			console.log('📋 Final Configuration:');
			console.log('   Ignore List:', config.ignoresList);
			console.log('   Hide Contents List:', config.hideContentsList);
			console.log('   Code Extensions:', config.codeExtensions);

			// Test list resolution
			const resolvedIgnores = ListConfigHelper.resolveListConfigToSet(config.ignoresList);
			const resolvedHides = ListConfigHelper.resolveListConfigToSet(config.hideContentsList);
			const resolvedCodeExtensions = ListConfigHelper.resolveListConfigToSet(config.codeExtensions);

			console.log('\n🔧 Resolved Lists:');
			console.log('   🚫 Resolved Ignores:', Array.from(resolvedIgnores));
			console.log('   👁️  Resolved Hides:', Array.from(resolvedHides));
			console.log('   💻 Resolved Code Extensions:', Array.from(resolvedCodeExtensions));
			console.log('\n✅ Configuration test complete!');
			break;
		}

		default:
			console.log("❌ Error: Unknown or missing command.");
			console.log("\nAvailable commands:");
			console.log("  bun script map [<path>] [options]     - Create a simple file structure map (with config)");
			console.log("  bun script map:enhanced [<path>]      - Create enhanced structure map with metrics (with config)");
			console.log("  bun script config:test                - Test configuration loading");
			console.log("  bun script reinstall                  - Clean and reinstall dependencies");
			console.log("  bun script clean:imports              - Clean up TS import statements");
			console.log("\nNote: <path> is optional if 'rootDirectory' is set in .excelyticsrc.json");
			process.exit(1);
	}
}

if (import.meta.main) {
	main().catch((err) => {
		console.error("Script failed to execute:", err);
		process.exit(1);
	});
}*/