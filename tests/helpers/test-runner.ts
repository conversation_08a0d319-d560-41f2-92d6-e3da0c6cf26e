import { Enum<PERSON>lient<PERSON>rigin, AllowedOrigins, UserRoles } from 'excelytics.shared-internals';
import type { TestUserCreateData, TestUser } from './constants/test.types';
import { analyzeToken } from '@/utils/token-analysis.utils';
import type { Identity } from '@/models/identity.model';
import IdentityModel from '@/models/identity.model';
import { testLogger } from './helpers/test-logger';
import { expect } from 'bun:test';
import mongoose from 'mongoose';

/**
 * Comprehensive Test Configuration and Utilities for Introspection.Finance.Identity
 *
 * This file provides centralized test configuration and reusable utilities
 * for the complete test suite with proper setup and teardown.
 */

// Test configuration
export const TEST_CONFIG = {
	// Test timeouts
	DEFAULT_TIMEOUT: 30000, // 30 seconds
	INTEGRATION_TIMEOUT: 60000, // 1 minute for integration tests

	// Test data cleanup
	CLEANUP_ENABLED: true,

	// Test user prefixes to identify test data
	TEST_EMAIL_PREFIX: 'test-',

	// Database settings for tests
	TEST_DB_NAME: 'introspection_identity_test',

	// Rate limiting test settings
	RATE_LIMIT_TEST_ATTEMPTS: 12,
	RATE_LIMIT_TIMEOUT: 5000,

	// Performance test thresholds
	PERFORMANCE_THRESHOLDS: {
		HEALTH_CHECK_MAX_TIME: 1000, // 1 second
		AUTH_REQUEST_MAX_TIME: 2000, // 2 seconds
		TOKEN_VALIDATION_MAX_TIME: 500, // 500ms
		DEEP_HEALTH_CHECK_MAX_TIME: 5000 // 5 seconds
	}
} as const;

// Test utilities
export class TestUtils {
	/**
	 * Generate a unique test email
	 */
	static generateTestEmail(prefix: string = 'test'): string {
		const timestamp = Date.now();
		const random = Math.random().toString(36).substring(7);
		return `${prefix}-${timestamp}-${random}@example.com`;
	}

	/**
	 * Generate test user data
	 */
	static generateTestUser(emailPrefix: string = 'test'): TestUser {
		return {
			clientId: new mongoose.Types.ObjectId(),
			clientOrigin: EnumClientOrigin.Excelytics,
			email: this.generateTestEmail(emailPrefix),
			password: 'TestPassword@123',
			isActive: true
		};
	}

	/**
	 * Generate test user data compatible with Identity model
	 */
	static generateTestUserData(emailPrefix: string = 'test'): TestUserCreateData {
		return {
			clientId: new mongoose.Types.ObjectId().toString(),
			clientOrigin: EnumClientOrigin.Excelytics,
			email: this.generateTestEmail(emailPrefix),
			password: 'TestPassword@123',
			isActive: true,
			roles: [UserRoles.USER]
		};
	}

	/**
	 * Create Identity model instance for testing
	 */
	static createIdentityInstance(emailPrefix: string = 'test'): Identity {
		const userData = this.generateTestUserData(emailPrefix);
		return new IdentityModel(userData);
	}

	/**
	 * Wait for a specified amount of time
	 */
	static async wait(ms: number): Promise<void> {
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	/**
	 * Retry a function with exponential backoff
	 */
	static async retry<T>(fn: () => Promise<T>, maxAttempts: number = 3, baseDelay: number = 1000): Promise<T> {
		let lastError: Error;

		for (let attempt = 1; attempt <= maxAttempts; attempt++) {
			try {
				return await fn();
			} catch (error) {
				lastError = error as Error;

				if (attempt === maxAttempts) {
					throw lastError;
				}

				const delay = baseDelay * Math.pow(2, attempt - 1);
				await this.wait(delay);
			}
		}

		throw lastError!;
	}

	/**
	 * Validate JWT token structure
	 */
	static validateJWTStructure(token: string): boolean {
		const parts = token.split('.');
		return parts.length === 3;
	}

	/**
	 * Extract claims from JWT without verification (for testing)
	 * Uses SharedInternals analyzeToken for consistency
	 */
	static extractJWTClaims(token: string): any {
		try {
			const analysis = analyzeToken(token);
			return analysis.isValid ? analysis.payload : null;
		} catch (error) {
			testLogger.error('Failed to extract JWT claims:', error);
			return null;
		}
	}

	/**
	 * Validate response structure
	 */
	static validateSuccessResponse(response: any): boolean {
		return (
			response &&
			typeof response.success === 'boolean' &&
			response.success === true &&
			response.data !== undefined
		);
	}

	/**
	 * Validate error response structure
	 */
	static validateErrorResponse(response: any): boolean {
		return (
			response &&
			typeof response.success === 'boolean' &&
			response.success === false &&
			response.error !== undefined
		);
	}

	/**
	 * Check if response time is within threshold
	 */
	static isResponseTimeAcceptable(startTime: number, endTime: number, threshold: number): boolean {
		return endTime - startTime <= threshold;
	}

	/**
	 * Generate multiple test users for concurrent testing
	 */
	static generateMultipleTestUsers(count: number, prefix: string = 'concurrent'): any[] {
		return Array(count)
			.fill(null)
			.map((_, index) => this.generateTestUser(`${prefix}-${index}`));
	}

	/**
	 * Validate security headers
	 */
	static validateSecurityHeaders(headers: any): boolean {
		const requiredHeaders = [
			'x-frame-options',
			'x-content-type-options',
			'x-xss-protection',
			'x-permitted-cross-domain-policies'
		];

		return requiredHeaders.every(header => headers[header] !== undefined);
	}

	/**
	 * Validate CORS headers using SharedInternals AllowedOrigins
	 */
	static validateCORSHeaders(headers: any, expectedOrigin?: string): boolean {
		if (expectedOrigin) {
			// Check if the expected origin is in the allowed origins list
			const isAllowedOrigin = AllowedOrigins.includes(expectedOrigin as any);
			const hasCorrectHeader = headers['access-control-allow-origin'] === expectedOrigin;
			return isAllowedOrigin && hasCorrectHeader;
		}
		return headers['access-control-allow-credentials'] === 'true';
	}

	/**
	 * Check if an origin is allowed according to SharedInternals configuration
	 */
	static isOriginAllowed(origin: string): boolean {
		return AllowedOrigins.includes(origin as any);
	}

	/**
	 * Clean up test data by email pattern
	 */
	static async cleanupTestUsers(request: any, emailPattern: string): Promise<void> {
		if (!TEST_CONFIG.CLEANUP_ENABLED) {
			return;
		}

		try {
			// This would require admin access or a special cleanup endpoint
			testLogger.log(`Cleaning up test users matching pattern: ${emailPattern}`);
			// Implementation would depend on available cleanup endpoints
		} catch (error) {
			testLogger.warn('Failed to cleanup test users:', error);
		}
	}
}

// Test data factory
export class TestDataFactory {
	/**
	 * Create valid registration data
	 */
	static createValidRegistrationData(overrides: any = {}) {
		return {
			...TestUtils.generateTestUser(),
			...overrides
		};
	}

	/**
	 * Create valid login data
	 */
	static createValidLoginData(email: string, password: string) {
		return { email, password };
	}

	/**
	 * Create invalid registration data for testing validation
	 */
	static createInvalidRegistrationData(): any[] {
		return [
			// Missing email
			{
				password: 'Test@123',
				clientId: new mongoose.Types.ObjectId(),
				clientOrigin: EnumClientOrigin.Excelytics,
				isActive: true
			},
			// Invalid email format
			{
				email: 'invalid-email',
				password: 'Test@123',
				clientId: new mongoose.Types.ObjectId(),
				clientOrigin: EnumClientOrigin.Excelytics,
				isActive: true
			},
			// Weak password
			{
				email: TestUtils.generateTestEmail('invalid'),
				password: '123',
				clientId: new mongoose.Types.ObjectId(),
				clientOrigin: EnumClientOrigin.Excelytics,
				isActive: true
			},
			// Missing required fields
			{
				email: TestUtils.generateTestEmail('invalid')
			}
		];
	}

	/**
	 * Create test data for rate limiting tests
	 */
	static createRateLimitTestData() {
		return {
			email: TestUtils.generateTestEmail('ratelimit'),
			password: 'WrongPassword@123'
		};
	}

	/**
	 * Create test data for CORS tests using proper SharedInternals AllowedOrigins
	 */
	static createCORSTestData() {
		return {
			// Use the actual allowed origins from SharedInternals
			allowedOrigins: [...AllowedOrigins],
			// prettier-ignore
			disallowedOrigins: [
				'http://malicious-site.com',
				'https://evil.example.com',
				'http://phishing-site.net',
				'http://unauthorized-localhost:8080',
				'https://fake-excelytics.com'
			]
		};
	}
}

// Test assertions
export class TestAssertions {
	/**
	 * Assert that response is a successful authentication response
	 */
	static assertSuccessfulAuth(response: any): void {
		expect(response.status).toBe(200);
		expect(TestUtils.validateSuccessResponse(response.body)).toBe(true);
		expect(response.body.data).toHaveProperty('token');
		expect(response.body.data).toHaveProperty('refreshToken');
		expect(TestUtils.validateJWTStructure(response.body.data.token)).toBe(true);
	}

	/**
	 * Assert that response is a valid token introspection response
	 */
	static assertValidTokenIntrospection(response: any, shouldBeActive: boolean = true): void {
		expect(response.status).toBe(200);
		expect(response.body).toHaveProperty('active', shouldBeActive);

		if (shouldBeActive) {
			expect(response.body).toHaveProperty('sub');
			expect(response.body).toHaveProperty('iss', 'excelytics.identity');
			expect(response.body).toHaveProperty('token_type', 'Bearer');
		}
	}

	/**
	 * Assert that response time is acceptable
	 */
	static assertResponseTime(startTime: number, endTime: number, threshold: number): void {
		const responseTime = endTime - startTime;
		expect(responseTime).toBeLessThan(threshold);
	}

	/**
	 * Assert that error response has proper structure
	 */
	static assertErrorResponse(response: any, expectedStatus: number): void {
		expect(response.status).toBe(expectedStatus);
		expect(TestUtils.validateErrorResponse(response.body)).toBe(true);
	}

	/**
	 * Assert that response has proper security headers
	 */
	static assertSecurityHeaders(response: any): void {
		expect(response.header).toHaveProperty('x-frame-options');
		expect(response.header).toHaveProperty('x-content-type-options');
		expect(response.header).toHaveProperty('x-xss-protection');
		expect(response.header).toHaveProperty('x-permitted-cross-domain-policies');
	}

	/**
	 * Assert CORS headers are properly configured for allowed origins
	 */
	static assertCORSHeaders(response: any, origin: string): void {
		if (TestUtils.isOriginAllowed(origin)) {
			expect(response.header['access-control-allow-origin']).toBe(origin);
			expect(response.header['access-control-allow-credentials']).toBe('true');
		} else {
			// For disallowed origins, the request should be blocked or not have CORS headers
			expect(response.status).toBe(403);
		}
	}
}

// Export test configuration and utilities
export default {
	TEST_CONFIG,
	TestUtils,
	TestDataFactory,
	TestAssertions
};