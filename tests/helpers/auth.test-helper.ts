import {
	type TokenGenerationInput,
	EnumClient<PERSON>rig<PERSON>,
	EnumClientPath,
	EnumTokenType,
	HttpStatus,
	UserRoles
} from 'excelytics.shared-internals';
import type { TestUserCreateData, AuthTokens, TestUser } from '../constants/test.types';
import { logTokenAnalysis } from '@/utils/token-analysis.utils';
import IdentityModel from '@/models/identity.model';
import { testLogger } from './test-logger';
import { env_idp } from '@app-types/env_';
import supertest from 'supertest';
import mongoose from 'mongoose';
import jwt from 'jsonwebtoken';
import app from '@/server';

const request = supertest(app);

export class AuthTestHelper {
	// Track created users for cleanup
	private static createdUsers: string[] = [];

	// Admin user for cleanup operations
	private static adminUser: {
		email: string;
		password: string;
		clientId: string;
		clientOrigin: number;
		accessToken?: string;
	} | null = null;

	/**
	 * Generate a unique test email to avoid conflicts
	 * @param prefix - Prefix for the email
	 * @returns Unique test email with body length between 8-13
	 */
	static generateTestEmail(prefix: string = 'test'): string {
		const timestamp = Date.now();
		const random = Math.random().toString(36).substring(7);
		return `${prefix}-${timestamp}-${random}@example.com`;
	}

	/**
	 * Create a test user object
	 * @param emailPrefix - Prefix for the email
	 * @param roles - Roles for the user
	 * @returns Test user object
	 */
	static createTestUser(emailPrefix: string = 'test', roles: string[] = [UserRoles.USER]): TestUser {
		const newUserObj: TestUser = {
			email: this.generateTestEmail(emailPrefix),
			clientOrigin: EnumClientOrigin.Excelytics,
			clientId: new mongoose.Types.ObjectId(),
			password: 'TestPassword@123',
			isActive: true,
			roles: roles
		};

		const textGuard = roles.includes(UserRoles.ADMIN) ? 'Admin' : emailPrefix;
		console.log(`🔐 Creating '${textGuard}' test-user with roles: ${roles.join(', ')}`, {
			clientId: newUserObj.clientId.toString(),
			password: newUserObj.password,
			email: newUserObj.email
		});

		return newUserObj;
	}

	/**
	 * Create test user data compatible with Identity model
	 * @param emailPrefix - Prefix for the email
	 * @returns Test user data for Identity model creation
	 */
	static createTestUserData(emailPrefix: string = 'test'): TestUserCreateData {
		return {
			clientId: new mongoose.Types.ObjectId().toString(),
			clientOrigin: EnumClientOrigin.Excelytics,
			email: this.generateTestEmail(emailPrefix),
			password: 'TestPassword@123',
			isActive: true,
			roles: [UserRoles.USER]
		};
	}

	/**
	 * Get admin access token
	 * @returns Admin access token if available, null otherwise
	 */
	static getAdminToken(): string | null {
		return this.adminUser?.accessToken || null;
	}

	/**
	 * Manually saves the user to the database and execute the login endpoint
	 * This allows us to acquire a valid admin token from the authentic IdP login endpoint
	 *
	 * Essentially, creates and sets up the admin user for cleanup operations
	 * @returns True if admin user is already set, false otherwise
	 */
	static async setupAdminUser(): Promise<boolean> {
		if (this.adminUser?.accessToken) {
			testLogger.log('👑 Admin user already setup');
			return true;
		}

		try {
			console.log('👑 Setting up admin user for cleanup operations...');

			const bcrypt = await import('bcryptjs');

			// Create admin user data using helper and set admin & user roles
			const adminUserData = this.createTestUserData('cleanup-admin');
			adminUserData.roles = [UserRoles.USER, UserRoles.ADMIN];

			// Hash the password and create the admin user Identity object
			const hashedPassword = await bcrypt.hash(adminUserData.password, 10);
			const newAdmin = new IdentityModel({
				...adminUserData,
				password: hashedPassword
			});

			// Manually save record to db, instead of endpoint testing:
			// This ensures endpoint testing will use a valid user from the db
			await newAdmin.save();
			testLogger.log('👑 Admin user created in database:', adminUserData.email);

			// Now, Login to acquire admin token
			await this.wait(500);
			const loginResponse = await request.post('/api/v1/auth/login').send({
				email: adminUserData.email,
				password: adminUserData.password,
				clientId: adminUserData.clientId,
				clientOrigin: adminUserData.clientOrigin
			});

			// Ensure successful response with generated admin token
			// If successful, set data to static admin user object
			if (loginResponse.status === HttpStatus.OK && loginResponse.body.data?.token) {
				this.adminUser = {
					email: adminUserData.email,
					password: adminUserData.password,
					clientId: adminUserData.clientId,
					clientOrigin: adminUserData.clientOrigin,
					accessToken: loginResponse.body.data.token
				};

				testLogger.log('👑 Admin user logged in successfully');

				// Analyze admin token
				logTokenAnalysis(this.adminUser.accessToken as string, 'Admin Token');

				return true;
			} else {
				testLogger.error('❌ Failed to login admin user:', loginResponse.status, loginResponse.body);
				return false;
			}
		} catch (error) {
			testLogger.error('❌ Error setting up admin user:', error);
			return false;
		}
	}

	/** Adds a user email to the cleanup list */
	static trackUserForCleanup(email: string): void {
		if (!this.createdUsers.includes(email)) {
			this.createdUsers.push(email);
		}
	}

	/** Clean up all tracked users using admin privileges */
	static async cleanupAllUsers(): Promise<void> {
		if (this.createdUsers.length === 0) {
			testLogger.debug('🧹 No users to cleanup');
			return;
		}

		testLogger.log(`🧹 Starting cleanup of ${this.createdUsers.length} users...`);

		// Check if admin user is already available (should be from beforeAll)
		if (!this.adminUser?.accessToken) {
			// Only setup admin user if not already available
			testLogger.log('👑 Admin user not found, setting up for cleanup...');
			const adminReady = await this.setupAdminUser();
			if (!adminReady || !this.adminUser?.accessToken) {
				testLogger.error('❌ Cannot cleanup users - admin user not available');
				return;
			}
		} else {
			testLogger.log('👑 Admin user already setup');
		}

		// Delete all tracked users
		for (const email of this.createdUsers) {
			try {
				const deleted = await this.deleteUserAsAdmin(this.adminUser.accessToken, email);
				if (deleted) {
					testLogger.info('✅ Cleaned up user:', email);
				} else {
					testLogger.debug('⚠️ Failed to cleanup user:', email);
				}

				// Small delay between deletions
				await this.wait(200);
			} catch (error: any) {
				testLogger.debug('⚠️ Error cleaning up user:', email, error.message);
			}
		}

		// Clean up admin user itself
		try {
			await this.deleteUserAsAdmin(this.adminUser.accessToken, this.adminUser.email);
			testLogger.debug('✅ Cleaned up admin user');
		} catch (error: any) {
			testLogger.debug('⚠️ Failed to cleanup admin user', this.adminUser.email, error.message);
		}

		// Reset tracking
		this.createdUsers = [];
		this.adminUser = null;

		testLogger.log('🧹 Cleanup completed');
	}

	/**
	 * Delete a user using admin privileges
	 * @param adminToken - Admin access token
	 * @param email - Email of user to delete
	 */
	static async deleteUserAsAdmin(adminToken: string, email: string): Promise<boolean> {
		try {
			const response = await request
				.delete(`/api/v1/identity/email/${email}`)
				.set('Authorization', `Bearer ${adminToken}`);

			testLogger.info(`🗑️ Delete attempt for ${email}:`, response.status, response.body?.message || 'No message');

			return response.status === 200;
		} catch (error: any) {
			testLogger.error(`❌ Error deleting user ${email}:`, error.message);
			return false;
		}
	}

	/** Register a new test user and return authentication tokens */
	static async registerAndLogin(user: TestUser): Promise<AuthTokens | null> {
		try {
			// Wait to avoid rate limiting
			await this.wait(1000);

			// Register the user
			const registerResponse = await request.post('/api/v1/auth/register').send(user);

			if (![200, 201].includes(registerResponse.status) || !registerResponse.body.data?.token) {
				testLogger.error('❌ Registration failed:', registerResponse.status, registerResponse.body);
				return null;
			}

			testLogger.log('✅ Registration successful for:', user.email);

			// Track user for cleanup
			this.trackUserForCleanup(user.email);
			testLogger.info('📊 Registration Response Data:', {
				hasToken: !!registerResponse.body.data.token,
				hasRefreshToken: !!registerResponse.body.data.refreshToken,
				hasUser: !!registerResponse.body.data.user,
				tokenLength: registerResponse.body.data.token?.length || 0,
				refreshTokenLength: registerResponse.body.data.refreshToken?.length || 0
			});

			// Analyze the tokens using our utilities
			if (registerResponse.body.data.token) {
				logTokenAnalysis(registerResponse.body.data.token, 'Access Token from Registration');
			}
			if (registerResponse.body.data.refreshToken) {
				logTokenAnalysis(registerResponse.body.data.refreshToken, 'Refresh Token from Registration');
			}

			if (registerResponse.body.data.user) {
				testLogger.info('👤 User object from registration:', {
					id: registerResponse.body.data.user._id || registerResponse.body.data.user.id,
					email: registerResponse.body.data.user.email,
					clientId: registerResponse.body.data.user.clientId,
					clientOrigin: registerResponse.body.data.user.clientOrigin,
					isActive: registerResponse.body.data.user.isActive,
					hasPassword: !!registerResponse.body.data.user.password,
					createdAt: registerResponse.body.data.user.createdAt,
					updatedAt: registerResponse.body.data.user.updatedAt
				});
			}

			return {
				accessToken: registerResponse.body.data.token,
				refreshToken: registerResponse.body.data.refreshToken || '',
				user: registerResponse.body.data.user
			} as AuthTokens;
		} catch (error) {
			testLogger.error('Error in registerAndLogin:', error);
			return null;
		}
	}

	/** Wait for specified milliseconds */
	static async wait(ms: number): Promise<void> {
		return new Promise(resolve => setTimeout(resolve, ms));
	}

	/** Generate an expired token for testing */
	static async generateExpiredToken(): Promise<string> {
		// Create a test payload similar to what would be in a real token
		const testPayload: TokenGenerationInput = {
			userId: new mongoose.Types.ObjectId().toString(),
			email: '<EMAIL>',
			clientId: new mongoose.Types.ObjectId().toString(),
			clientOrigin: EnumClientOrigin.Excelytics,
			clientPath: EnumClientPath.Identity,
			isActive: true,
			permissions: ['read:profile']
		};

		// Add issuedAt as Unix timestamp for Zod validation
		const issuedAtTimestamp = Math.floor(Date.now() / 1000);
		const signPayload = {
			...testPayload,
			tokenType: EnumTokenType.ACCESS,
			issuedAt: issuedAtTimestamp
		};

		// Generate a token that expired 1 hour ago
		const expiredToken = jwt.sign(signPayload, env_idp.JWT_SECRET as string, {
			expiresIn: '-1h' // This creates a token that expired 1 hour ago
		});

		if (env_idp.TEST_VERBOSE_LOGGING) {
			console.log('🧪 Generated expired token for testing:');
			console.log(`   Token: ${expiredToken.substring(0, 50)}...`);
			console.log(`   Expired: 1 hour ago`);
		}

		return expiredToken;
	}

	/** Generate a token that will expire very soon (for real-time expiration testing) */
	static async generateShortLivedToken(expiresInSeconds: number = 2): Promise<string> {
		const testPayload: TokenGenerationInput = {
			userId: new mongoose.Types.ObjectId().toString(),
			email: '<EMAIL>',
			clientId: new mongoose.Types.ObjectId().toString(),
			clientOrigin: EnumClientOrigin.Excelytics,
			clientPath: EnumClientPath.Identity,
			isActive: true,
			permissions: ['read:profile']
		};

		const issuedAtTimestamp = Math.floor(Date.now() / 1000);
		const signPayload = {
			...testPayload,
			tokenType: EnumTokenType.ACCESS,
			issuedAt: issuedAtTimestamp
		};

		const shortLivedToken = jwt.sign(signPayload, env_idp.JWT_SECRET as string, {
			expiresIn: `${expiresInSeconds}s`
		});

		if (env_idp.TEST_VERBOSE_LOGGING) {
			console.log(`🧪 Generated short-lived token (expires in ${expiresInSeconds}s):`);
			console.log(`   Token: ${shortLivedToken.substring(0, 50)}...`);
		}

		return shortLivedToken;
	}

	/** Generate a malformed token for testing */
	static generateMalformedToken(): string {
		// Create a token with invalid signature
		const validHeader = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64url');
		const validPayload = Buffer.from(JSON.stringify({
			userId: 'test-user-id',
			email: '<EMAIL>',
			exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
		})).toString('base64url');
		const invalidSignature = 'invalid_signature_that_will_fail_verification';

		return `${validHeader}.${validPayload}.${invalidSignature}`;
	}

	/** Generate a token with valid signature but invalid payload structure */
	static generateInvalidPayloadToken(): string {
		// Create a payload that will fail Zod validation
		const invalidPayload = {
			// Missing required fields like userId, email, etc.
			invalidField: 'this-should-not-be-here',
			someOtherField: 'also-invalid'
		};

		const issuedAtTimestamp = Math.floor(Date.now() / 1000);
		const signPayload = {
			...invalidPayload,
			issuedAt: issuedAtTimestamp
		};

		// This will have a valid signature but invalid payload structure
		// Don't set exp in payload, let expiresIn option handle it
		const invalidStructureToken = jwt.sign(signPayload, env_idp.JWT_SECRET as string, {
			expiresIn: '1h'
		});

		if (env_idp.TEST_VERBOSE_LOGGING) {
			console.log('🧪 Generated token with invalid payload structure:');
			console.log(`   Token: ${invalidStructureToken.substring(0, 50)}...`);
		}

		return invalidStructureToken;
	}

	/** Test rate limiting by making multiple requests */
	static async testRateLimit(endpoint: string, payload: any, maxAttempts: number = 15): Promise<number[]> {
		const promises = [];
		for (let i = 0; i < maxAttempts; i++) {
			promises.push(request.post(endpoint).send(payload));
		}

		const responses = await Promise.all(promises);
		return responses.map(res => res.status);
	}
}

export default AuthTestHelper;