import { TestDataFactory, TestAssertions, TestUtils } from './test-runner';
import { TokenAnalysisHelper } from './helpers/token-analysis-helper';
import { beforeAll, describe, afterAll, expect, it } from 'bun:test';
import type { AuthTokens, TestUser } from './constants/test.types';
import { EnumClientOrigin } from 'excelytics.shared-internals';
import AuthTestHelper from './helpers/auth.test-helper';
import supertest from 'supertest';
import mongoose from 'mongoose';
import app from '@/server';
import { TestSetup } from './helpers/test-runner.helper';

const request = supertest(app);

describe('[REJECTIONS] Security Rejection Tests', () => {
	let testUser: TestUser;
	let tokens: AuthTokens | null = null;

	beforeAll(async () => {
		const result = await TestSetup.commonBeforeAllLogic('security-rejection');
		testUser = result.testUser;
		tokens = result.tokens;
	});

	afterAll(async () => {
		await TestSetup.commonAfterAllLogic();
	});

	describe('Authentication Middleware Rejections', () => {

		it('Should reject requests without Authorization header', async () => {
			const response = await request.get(`/api/v1/identity/email/${testUser.email}`);
			expect(response.status).toBe(401);
		});

		it('Should reject requests with invalid Bearer token format', async () => {
			const response = await request
				.get(`/api/v1/identity/email/${testUser.email}`)
				.set('Authorization', 'InvalidFormat token');
			expect(response.status).toBe(401);
		});

		it('Should reject requests with malformed JWT token', async () => {
			const malformedToken = AuthTestHelper.generateMalformedToken();
			const response = await request
				.get(`/api/v1/identity/email/${testUser.email}`)
				.set('Authorization', `Bearer ${malformedToken}`);
			expect(response.status).toBe(401);
		});

		it('Should reject expired tokens', async () => {
			const expiredToken = await AuthTestHelper.generateExpiredToken();
			const response = await request
				.get(`/api/v1/identity/email/${testUser.email}`)
				.set('Authorization', `Bearer ${expiredToken}`);
			expect(response.status).toBe(401);
		});

	});

	describe('Payload and Input Rejections', () => {
		it('Should reject oversized JSON payloads', async () => {
			const largePayload = {
				email: '<EMAIL>',
				password: 'a'.repeat(50 * 1024 * 1024), // 50MB string
				data: 'x'.repeat(50 * 1024 * 1024)
			};
			const response = await request.post('/api/v1/auth/register').send(largePayload);
			expect(response.status).toBe(413); // Payload Too Large
		});

		it('Should reject invalid registration data', async () => {
			const invalidDataSets = TestDataFactory.createInvalidRegistrationData();
			for (const invalidData of invalidDataSets) {
				const response = await request.post('/api/v1/auth/register').send(invalidData);
				expect([400, 422, 429]).toContain(response.status);

				if (response.status === 429) {
					console.log('⚠️ Security validation test hit rate limit - this is expected behavior');
				}
				TestAssertions.assertErrorResponse(response, response.status);
				await TestUtils.wait(500);
			}
		});

		it('Should handle SQL injection attempts gracefully', async () => {
			await AuthTestHelper.wait(2000);
			const sqlInjectionPayload = {
				email: "'; DROP TABLE users; --",
				password: 'Test@123'
			};
			const response = await request.post('/api/v1/auth/login').send(sqlInjectionPayload);
			expect([400, 429]).toContain(response.status);
		});

		it('Should sanitize and validate email inputs', async () => {
			await AuthTestHelper.wait(2000);
			const maliciousPayload = {
				email: '<script>alert("xss")</script>@example.com',
				password: 'Test@123',
				clientId: new mongoose.Types.ObjectId(),
				clientOrigin: EnumClientOrigin.Excelytics,
				isActive: true
			};
			const response = await request.post('/api/v1/auth/register').send(maliciousPayload);
			expect([400, 429]).toContain(response.status);
		});
	});

	describe('Rate Limiting Rejections', () => {
		it('Should apply rate limiting to login failures', async () => {
			const invalidCredentials = {
				email: testUser.email,
				password: 'WrongPassword@123'
			};
			const statusCodes = await AuthTestHelper.testRateLimit('/api/v1/auth/login', invalidCredentials, 12);
			const rateLimitedResponses = statusCodes.filter(status => status === 429);
			expect(rateLimitedResponses.length).toBeGreaterThan(0);
		});
	});
});

describe.skip('[ACCEPTANCE] Security Acceptance Tests', () => {
	let testUser: TestUser;
	let tokens: AuthTokens | null = null;

	beforeAll(async () => {
		const result = await TestSetup.commonBeforeAllLogic('security-acceptance');
		testUser = result.testUser;
		tokens = result.tokens;
	});

	afterAll(async () => {
		await TestSetup.commonAfterAllLogic();
	});

	describe('Valid Token Acceptance', () => {
		it('Should accept requests with valid Bearer token', async () => {
			if (!tokens?.accessToken) {
				console.warn('Skipping token test - no valid access token available');
				return;
			}

			console.log('🔍 Testing authentication with valid token...');
			const healthResponse = await request
				.get('/api/v1/health')
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			console.log('✅ Health endpoint response:', healthResponse.status);
			expect(healthResponse.status).toBe(200);

			const adminResponse = await request
				.get(`/api/v1/identity/email/${testUser.email}`)
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			console.log('🔒 Admin endpoint response:', adminResponse.status);
			console.log('📄 Admin endpoint message:', adminResponse.body.message);

			expect(adminResponse.status).not.toBe(401);
			expect(adminResponse.status).toBe(403); // Regular user accessing admin endpoint
		});

		it('Should validate token using verify endpoint', async () => {
			if (!tokens?.accessToken) {
				console.warn('Skipping token verification test - no valid access token available');
				return;
			}

			console.log('🔍 Verifying token with IdP...');
			console.log('📤 Sending token:', tokens.accessToken.substring(0, 50) + '...');

			const response = await request.post('/api/v1/verify-access-token').send({ token: tokens.accessToken });
			TokenAnalysisHelper.logTokenVerification(response, 'IdP Verification');

			expect(response.status).toBe(200);
			expect(response.body.success).toBe(true);
			expect(response.body.data.active).toBe(true);
		});

		it('Should use token for authenticated operations', async () => {
			if (!tokens?.accessToken) {
				console.warn('Skipping authenticated operations test - no valid access token available');
				return;
			}

			console.log('🔐 Testing authenticated operations...');
			console.log('📋 Testing user data access...');

			const userDataResponse = await request
				.get(`/api/v1/identity/email/${testUser.email}`)
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			TokenAnalysisHelper.logUserDataAccess(userDataResponse);

			const endpoints = [
				{ path: '/api/v1/health', description: 'Health Check' },
				{ path: '/api/v1/auth/session', description: 'Session Info' }
			];

			for (const endpoint of endpoints) {
				const response = await request
					.get(endpoint.path)
					.set('Authorization', `Bearer ${tokens.accessToken}`);

				TokenAnalysisHelper.logHealthCheck(response, endpoint.path);
				expect([200, 404]).toContain(response.status);
			}

			expect(userDataResponse.status).not.toBe(401);
		});

	});

	describe.skip('[OPERATIONS] Authenticated User Operations', () => {
		it('Should perform authenticated user operations', async () => {
			const testUser = AuthTestHelper.createTestUser('auth-ops');
			const tokens = await AuthTestHelper.registerAndLogin(testUser);

			if (!tokens) {
				throw new Error('Failed to create user for authenticated operations test');
			}

			console.log('Created user for auth ops:', testUser.email);
			console.log('Got tokens:', {
				accessToken: tokens.accessToken.substring(0, 50) + '...',
				hasRefreshToken: !!tokens.refreshToken
			});

			const verifyResponse = await request
				.post('/api/v1/verify-access-token')
				.send({ token: tokens.accessToken });

			expect(verifyResponse.status).toBe(200);
			expect(verifyResponse.body.success).toBe(true);
			expect(verifyResponse.body.data.active).toBe(true);

			const userDataResponse = await request
				.get(`/api/v1/identity/email/${testUser.email}`)
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			console.log('User data access response:', userDataResponse.status, userDataResponse.body);
			expect(userDataResponse.status).not.toBe(401);

			const healthResponse = await request
				.get('/api/v1/health')
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			expect(healthResponse.status).toBe(200);
		});

		it('Should handle token refresh operations', async () => {
			const testUser = AuthTestHelper.createTestUser('refresh-test');
			const tokens = await AuthTestHelper.registerAndLogin(testUser);

			if (!tokens || !tokens.refreshToken) {
				console.warn('Skipping refresh token test - no refresh token available');
				return;
			}

			console.log('Testing refresh token functionality');

			const accessTokenVerify = await request
				.post('/api/v1/verify-access-token')
				.send({ token: tokens.accessToken });

			expect(accessTokenVerify.status).toBe(200);
			expect(accessTokenVerify.body.data.active).toBe(true);
		});
	});

	describe.skip('[ANALYSIS] Token Analysis Tests', () => {
		it('Should create user and analyze complete token structure', async () => {
			console.log('\n🔬 === COMPREHENSIVE TOKEN ANALYSIS ===');

			const analysisUser = AuthTestHelper.createTestUser('token-analysis');
			console.log('🆕 Creating analysis user:', analysisUser.email);

			const tokens = await AuthTestHelper.registerAndLogin(analysisUser);

			if (!tokens) {
				throw new Error('Failed to create user for token analysis');
			}

			const verifyResponse = await request
				.post('/api/v1/verify-access-token')
				.send({ token: tokens.accessToken });

			TokenAnalysisHelper.logTokenVerification(verifyResponse);

			const authResponse = await request
				.get(`/api/v1/identity/email/${analysisUser.email}`)
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			TokenAnalysisHelper.logAuthenticatedRequest(authResponse);

			expect(tokens.accessToken).toBeDefined();
			expect(tokens.refreshToken).toBeDefined();
			expect(verifyResponse.status).toBe(200);
			expect(verifyResponse.body.data.active).toBe(true);

			console.log('🧹 Analysis user will be cleaned up by admin\n');
		});

		it('Should create admin user and test elevated permissions', async () => {
			console.log('\n👑 === ADMIN USER TESTING ===');

			const adminReady = await AuthTestHelper.setupAdminUser();
			expect(adminReady).toBe(true);

			const adminUser = (AuthTestHelper as any).adminUser;
			if (!adminUser?.accessToken) {
				throw new Error('Failed to get admin user token');
			}

			const adminTokens: AuthTokens = {
				accessToken: adminUser.accessToken,
				refreshToken: '',
				user: {
					id: adminUser.clientId,
					email: adminUser.email,
					clientId: adminUser.clientId,
					clientOrigin: adminUser.clientOrigin,
					isActive: true,
					roles: ['user', 'admin']
				}
			};

			TokenAnalysisHelper.comprehensiveTokenDemo(adminTokens, 'Admin User Testing');
			TokenAnalysisHelper.demonstrateRequestUserFunctionality(adminTokens.accessToken, 'Admin User Testing');

			const regularUser = AuthTestHelper.createTestUser('to-be-deleted');
			const regularTokens = await AuthTestHelper.registerAndLogin(regularUser);

			if (!regularTokens) {
				throw new Error('Failed to create regular user for deletion test');
			}

			console.log('🎯 Testing admin deletion capabilities...');
			const deletionSuccess = await AuthTestHelper.deleteUserAsAdmin(adminTokens.accessToken, regularUser.email);

			console.log(
				'🗑️ Admin deletion result:',
				deletionSuccess ? '✅ Success' : '⚠️ Failed (expected if permissions not implemented)'
			);

			expect(adminTokens.accessToken).toBeDefined();
			expect(adminTokens.refreshToken).toBeDefined();
		});

		it('Should demonstrate Express Request.user functionality', async () => {
			console.log('\n🔧 === EXPRESS REQUEST.USER DEMONSTRATION ===');

			const testUser = AuthTestHelper.createTestUser('express-demo');
			const tokens = await AuthTestHelper.registerAndLogin(testUser);

			if (!tokens) {
				throw new Error('Failed to create user for Express demo');
			}

			TokenAnalysisHelper.comprehensiveTokenDemo(tokens, 'Express Integration');
			TokenAnalysisHelper.demonstrateRequestUserFunctionality(tokens.accessToken);

			console.log('\n🔍 === TESTING REQUEST.USER ANALYSIS ===');
			console.log('📝 Raw Token Analysis:');

			const healthResponse = await request
				.get('/api/v1/health')
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			TokenAnalysisHelper.logHealthCheck(healthResponse, '/api/v1/health');

			expect(tokens.accessToken).toBeDefined();
			expect(tokens.refreshToken).toBeDefined();
		});

	});

	describe.skip('[VALIDATION] Input Validation Tests', () => {
		it('Should sanitize and validate email inputs', async () => {
			await AuthTestHelper.wait(2000);

			const maliciousPayload = {
				email: '<script>alert("xss")</script>@example.com',
				password: 'Test@123',
				clientId: new mongoose.Types.ObjectId(),
				clientOrigin: EnumClientOrigin.Excelytics,
				isActive: true
			};

			const response = await request
				.post('/api/v1/auth/register')
				.send(maliciousPayload);

			expect([400, 429]).toContain(response.status);
		});
	});

	describe.skip('[RATE-LIMITING] Rate Limiting Tests', () => {
		let testUser: TestUser;

		beforeAll(async () => {
			const result = await TestSetup.commonBeforeAllLogic('rate-limit');
			testUser = result.testUser;
		});

		it('Should apply rate limiting to login failures', async () => {
			const invalidCredentials = {
				email: testUser.email,
				password: 'WrongPassword@123'
			};

			const statusCodes = await AuthTestHelper.testRateLimit('/api/v1/auth/login', invalidCredentials, 12);
			const rateLimitedResponses = statusCodes.filter(status => status === 429);
			expect(rateLimitedResponses.length).toBeGreaterThan(0);
		});

		it('Should include rate limit headers', async () => {
			const response = await request.get('/api/v1/health');

			const hasRateLimitHeaders =
				response.header['x-ratelimit-limit'] ||
				response.header['x-ratelimit-remaining'] ||
				response.header['x-ratelimit-reset'] ||
				response.header['ratelimit-limit'] ||
				response.header['ratelimit-remaining'];

			expect(response.status).toBe(200);
		});

		it('Should reset rate limit after time window', async () => {
			await new Promise(resolve => setTimeout(resolve, 1000));
			const response = await request.get('/api/v1/health');
			expect(response.status).toBe(200);
		});
	});
});