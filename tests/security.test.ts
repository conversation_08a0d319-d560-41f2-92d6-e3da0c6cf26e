import { TestDataFactory, TestAssertions, TestUtils } from './helpers/test-runner.ts';
import { EnumClientOrigin, HttpStatus } from 'excelytics.shared-internals';
import { TokenAnalysisHelper } from './helpers/token-analysis-helper';
import { beforeAll, describe, afterAll, expect, it } from 'bun:test';
import type { AuthTokens, TestUser } from './constants/test.types';
import { TestSetup } from './helpers/test-runner.helper';
import AuthTestHelper from './helpers/auth.test-helper';
import supertest from 'supertest';
import mongoose from 'mongoose';
import app from '@/server';

const request = supertest(app);

describe.skip('[REJECTIONS] Security Rejection Tests', () => {
	let testUser: TestUser;
	let tokens: AuthTokens | null = null;

	beforeAll(async () => {
		const result = await TestSetup.commonBeforeAllLogic('security-rejection');
		testUser = result.testUser;
		tokens = result.tokens;
	});

	afterAll(async () => {
		await TestSetup.commonAfterAllLogic();
	});

	describe('Authentication Middleware Rejections', () => {
		it('Should reject requests without Authorization header', async () => {
			const response = await request.get(`/api/v1/identity/email/${testUser.email}`);

			expect(response.status).toBe(HttpStatus.UNAUTHORIZED);
		});

		it('Should reject requests with invalid Bearer token format', async () => {
			const response = await request
				.get(`/api/v1/identity/email/${testUser.email}`)
				.set('Authorization', 'InvalidFormat token');

			expect(response.status).toBe(HttpStatus.UNAUTHORIZED);
		});

		it('Should reject requests with malformed JWT token', async () => {
			const malformedToken = AuthTestHelper.generateMalformedToken();
			const response = await request
				.get(`/api/v1/identity/email/${testUser.email}`)
				.set('Authorization', `Bearer ${malformedToken}`);

			expect(response.status).toBe(HttpStatus.UNAUTHORIZED);
		});

		it('Should reject expired tokens', async () => {
			const expiredToken = await AuthTestHelper.generateExpiredToken();
			const response = await request
				.get(`/api/v1/identity/email/${testUser.email}`)
				.set('Authorization', `Bearer ${expiredToken}`);

			expect(response.status).toBe(HttpStatus.UNAUTHORIZED);
		});

	});

	describe('Payload and Input Rejections', () => {
		it('Should reject oversized JSON payloads', async () => {
			const largePayload = {
				email: '<EMAIL>',
				password: 'a'.repeat(50 * 1024 * 1024), // 50MB string
				data: 'x'.repeat(50 * 1024 * 1024) // 50MB string
			};
			const response = await request.post('/api/v1/auth/register').send(largePayload);
			expect(response.status).toBe(HttpStatus.PAYLOAD_TOO_LARGE); // Payload Too Large
		});

		it('Should reject invalid registration data', async () => {
			const invalidDataSets = TestDataFactory.createInvalidRegistrationData();
			for (const invalidData of invalidDataSets) {
				const response = await request.post('/api/v1/auth/register').send(invalidData);
				expect([HttpStatus.UNAUTHORIZED, HttpStatus.BAD_REQUEST]).toContain(response.status);

				if (response.status === HttpStatus.TOO_MANY_REQUESTS) {
					console.log('⚠️ Security validation test hit rate limit - this is expected behavior');
				}

				TestAssertions.assertErrorResponse(response, response.status);
				await TestUtils.wait(500);
			}
		});

		it('Should handle SQL injection attempts gracefully', async () => {
			await AuthTestHelper.wait(2000);
			const sqlInjectionPayload = {
				email: "'; DROP TABLE users; --",
				password: 'Test@123'
			};

			const response = await request.post('/api/v1/auth/login').send(sqlInjectionPayload);
			expect(response.status).toBe(HttpStatus.BAD_REQUEST);
		});

		it('Should sanitize and validate email inputs', async () => {
			await AuthTestHelper.wait(1000);
			const maliciousPayload = {
				email: '<script>alert("xss")</script>@example.com',
				clientOrigin: EnumClientOrigin.Excelytics,
				clientId: new mongoose.Types.ObjectId(),
				password: 'Test@123',
				isActive: true
			};

			const response = await request.post('/api/v1/auth/register').send(maliciousPayload);
			expect(response.status).toBe(HttpStatus.BAD_REQUEST);
		});
	});

	describe('Rate Limiting Rejections', () => {
		it('Should apply rate limiting to login failures', async () => {
			const invalidCredentials = {
				email: testUser.email,
				password: 'WrongPassword@123'
			};

			const statusCodes = await AuthTestHelper.testRateLimit(
				'/api/v1/auth/login',
				invalidCredentials,
				12
			);

			const rateLimitedResponses = statusCodes.filter(status => status === HttpStatus.UNAUTHORIZED);
			expect(rateLimitedResponses.length).toBeGreaterThan(0);
		});
	});
});

describe('[ACCEPTANCE] Security Acceptance Tests', () => {
	let testUser: TestUser;
	let tokens: AuthTokens | null = null;

	beforeAll(async () => {
		const result = await TestSetup.commonBeforeAllLogic('security-acceptance');
		testUser = result.testUser;
		tokens = result.tokens;
	});

	afterAll(async () => {
		await TestSetup.commonAfterAllLogic();
	});

	describe.skip('Valid Token Acceptance', () => {
		it('Should accept requests with valid Bearer token', async () => {
			if (!tokens?.accessToken) {
				console.warn('Skipping token test - no valid access token available');
				return;
			}

			const adminResponse = await request
				.get(`/api/v1/identity/email/${testUser.email}`)
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			console.log('📄 Admin endpoint message:', adminResponse.body.message);

			expect(adminResponse.status).not.toBe(HttpStatus.UNAUTHORIZED);
			expect(adminResponse.status).toBe(HttpStatus.FORBIDDEN);
		});

		it('Should validate token using verify endpoint', async () => {
			if (!tokens?.accessToken) {
				console.warn('Skipping token verification test - no valid access token available');
				return;
			}

			console.log('📤 Sending token:', tokens.accessToken.substring(0, 50) + '...');

			const response = await request.post('/api/v1/verify-access-token').send({ token: tokens.accessToken });
			TokenAnalysisHelper.logTokenVerification(response, 'IdP Verification');

			expect(response.body.success).toBe(true);
			expect(response.status).toBe(HttpStatus.OK);
			expect(response.body.data.active).toBe(true);
		});

		it('Should use token for authenticated operations', async () => {
			if (!tokens?.accessToken) {
				console.warn('Skipping authenticated operations test - no valid access token available');
				return;
			}

			const userDataResponse = await request
				.get(`/api/v1/identity/email/${testUser.email}`)
				.set('Authorization', `Bearer ${tokens.accessToken}`);

			// Only logged if test-logging is set to INFO
			TokenAnalysisHelper.logUserDataAccess(userDataResponse);

			const endpoints = [
				{ path: '/api/v1/health', description: 'Health Check' },
				{ path: '/api/v1/auth/session', description: 'Session Info' }
			];

			for (const endpoint of endpoints) {
				const response = await request
					.get(endpoint.path)
					.set('Authorization', `Bearer ${tokens.accessToken}`);

				// Only logged if test-logging is set to INFO
				TokenAnalysisHelper.logHealthCheck(response, endpoint.path);
				expect([HttpStatus.OK]).toContain(response.status);
			}

			expect(userDataResponse.status).not.toBe(HttpStatus.UNAUTHORIZED);
		});
	});

	describe('Authenticated User Operations', () => {
		it('Should perform authenticated user operations', async () => {
			const operationsUser = AuthTestHelper.createTestUser('auth-ops');
			const operationsTokens = await AuthTestHelper.registerAndLogin(operationsUser);

			if (!operationsTokens) {
				throw new Error('Failed to create user for authenticated operations test');
			}

			const verifyResponse = await request
				.post('/api/v1/verify-access-token')
				.send({ token: operationsTokens.accessToken });

			expect(verifyResponse.status).toBe(HttpStatus.OK);
			expect(verifyResponse.body.success).toBe(true);
			expect(verifyResponse.body.data.active).toBe(true);

			const userDataResponse = await request
				.get(`/api/v1/identity/email/${operationsUser.email}`)
				.set('Authorization', `Bearer ${operationsTokens.accessToken}`);

			console.log('User data access response:', userDataResponse.status, userDataResponse.body);
			expect(userDataResponse.status).not.toBe(401);

			const healthResponse = await request
				.get('/api/v1/health')
				.set('Authorization', `Bearer ${operationsTokens.accessToken}`);

			expect(healthResponse.status).toBe(200);
		});

		it('Should handle token refresh operations', async () => {
			const refreshUser = AuthTestHelper.createTestUser('refresh-test');
			const refreshTokens = await AuthTestHelper.registerAndLogin(refreshUser);

			if (!refreshTokens || !refreshTokens.refreshToken) {
				console.warn('Skipping refresh token test - no refresh token available');
				return;
			}

			console.log('Testing refresh token functionality');

			const accessTokenVerify = await request
				.post('/api/v1/verify-access-token')
				.send({ token: refreshTokens.accessToken });

			expect(accessTokenVerify.status).toBe(200);
			expect(accessTokenVerify.body.data.active).toBe(true);
		});
	});

	describe.skip('Rate Limiting Acceptance', () => {
		it('Should include rate limit headers', async () => {
			const response = await request.get('/api/v1/health');

			const hasRateLimitHeaders =
				response.header['x-ratelimit-limit'] ||
				response.header['x-ratelimit-remaining'] ||
				response.header['x-ratelimit-reset'] ||
				response.header['ratelimit-limit'] ||
				response.header['ratelimit-remaining'];

			expect(response.status).toBe(200);
		});

		it('Should reset rate limit after time window', async () => {
			await new Promise(resolve => setTimeout(resolve, 1000));
			const response = await request.get('/api/v1/health');
			expect(response.status).toBe(200);
		});
	});
});

describe.skip('Token Analysis Tests', () => {
	it('Should create user and analyze complete token structure', async () => {
		console.log('\n🔬 === COMPREHENSIVE TOKEN ANALYSIS ===');

		const analysisUser = AuthTestHelper.createTestUser('token-analysis');
		console.log('🆕 Creating analysis user:', analysisUser.email);

		const analysisTokens = await AuthTestHelper.registerAndLogin(analysisUser);

		if (!analysisTokens) {
			throw new Error('Failed to create user for token analysis');
		}

		const verifyResponse = await request
			.post('/api/v1/verify-access-token')
			.send({ token: analysisTokens.accessToken });

		TokenAnalysisHelper.logTokenVerification(verifyResponse);

		const authResponse = await request
			.get(`/api/v1/identity/email/${analysisUser.email}`)
			.set('Authorization', `Bearer ${analysisTokens.accessToken}`);

		TokenAnalysisHelper.logAuthenticatedRequest(authResponse);

		expect(analysisTokens.accessToken).toBeDefined();
		expect(analysisTokens.refreshToken).toBeDefined();
		expect(verifyResponse.status).toBe(200);
		expect(verifyResponse.body.data.active).toBe(true);

		console.log('🧹 Analysis user will be cleaned up by admin\n');
	});

	it('Should create admin user and test elevated permissions', async () => {
		console.log('\n👑 === ADMIN USER TESTING ===');

		const adminReady = await AuthTestHelper.setupAdminUser();
		expect(adminReady).toBe(true);

		const adminUser = (AuthTestHelper as any).adminUser;
		if (!adminUser?.accessToken) {
			throw new Error('Failed to get admin user token');
		}

		const adminTokens: AuthTokens = {
			accessToken: adminUser.accessToken,
			refreshToken: '',
			user: {
				id: adminUser.clientId,
				email: adminUser.email,
				clientId: adminUser.clientId,
				clientOrigin: adminUser.clientOrigin,
				isActive: true,
				roles: ['user', 'admin']
			}
		};

		TokenAnalysisHelper.comprehensiveTokenDemo(adminTokens, 'Admin User Testing');
		TokenAnalysisHelper.demonstrateRequestUserFunctionality(adminTokens.accessToken, 'Admin User Testing');

		const regularUser = AuthTestHelper.createTestUser('to-be-deleted');
		const regularTokens = await AuthTestHelper.registerAndLogin(regularUser);

		if (!regularTokens) {
			throw new Error('Failed to create regular user for deletion test');
		}

		console.log('🎯 Testing admin deletion capabilities...');
		const deletionSuccess = await AuthTestHelper.deleteUserAsAdmin(adminTokens.accessToken, regularUser.email);

		console.log(
			'🗑️ Admin deletion result:',
			deletionSuccess ? '✅ Success' : '⚠️ Failed (expected if permissions not implemented)'
		);

		expect(adminTokens.accessToken).toBeDefined();
		expect(adminTokens.refreshToken).toBeDefined();
	});

	it('Should demonstrate Express Request.user functionality', async () => {
		console.log('\n🔧 === EXPRESS REQUEST.USER DEMONSTRATION ===');

		const expressUser = AuthTestHelper.createTestUser('express-demo');
		const expressTokens = await AuthTestHelper.registerAndLogin(expressUser);

		if (!expressTokens) {
			throw new Error('Failed to create user for Express demo');
		}

		TokenAnalysisHelper.comprehensiveTokenDemo(expressTokens, 'Express Integration');
		TokenAnalysisHelper.demonstrateRequestUserFunctionality(expressTokens.accessToken);

		console.log('\n🔍 === TESTING REQUEST.USER ANALYSIS ===');
		console.log('📝 Raw Token Analysis:');

		const healthResponse = await request
			.get('/api/v1/health')
			.set('Authorization', `Bearer ${expressTokens.accessToken}`);

		TokenAnalysisHelper.logHealthCheck(healthResponse, '/api/v1/health');

		expect(expressTokens.accessToken).toBeDefined();
		expect(expressTokens.refreshToken).toBeDefined();
	});
});