trigger:
  - master

pr:
  branches:
    include:
      - master

variables:
  - group: SharedFeedCredentials

pool:
  name: 'Unraid'

jobs:
  - job: AnalyzeAndBuild
    displayName: 'Analyze and Build IdP Microservice'
    steps:
      - template: pipelines/steps/setup-environment.yaml
      - template: pipelines/steps/install-dependencies.yaml
      - template: pipelines/steps/analyze-structure.yaml

      - script: |
          echo "Generating build summary for IdP microservice..."
          TS_COUNT=$(find src/ -name "*.ts" 2>/dev/null | grep -v node_modules | wc -l || echo "0")
          TEST_COUNT=$(find tests/ -name "*.ts" 2>/dev/null | grep -v node_modules | wc -l || echo "0")
          TOTAL_FILES=$(find . -name "*.ts" -o -name "*.js" -o -name "*.json" 2>/dev/null | grep -v node_modules | wc -l || echo "0")

          cat > build-summary.md << EOF
          # 🚀 Build Summary - Introspection.Finance.Identity (IdP)

          ## 📊 Build Information
          - **Build Number:** $(Build.BuildNumber)
          - **Source Branch:** $(Build.SourceBranchName)
          - **Commit:** $(Build.SourceVersion)
          - **Build Time:** $(date)
          - **Microservice:** Identity Provider (IdP)

          ## 📦 Analysis
          - **Source TypeScript Files:** $TS_COUNT
          - **Test Files:** $TEST_COUNT
          - **Total Project Files:** $TOTAL_FILES
          - **Structure File:** $(StructureFileName)

          ## 🏗️ Microservice Architecture
          - **Service:** Introspection.Finance.Identity
          - **Type:** Identity Provider & Authentication Service
          - **Tech Stack:** MongoDB, TypeScript, Express, Bun
          - **Deployment Target (future):** Unraid Server with Nginx Gateway
          EOF
        displayName: 'Generate IdP Build Summary'

      - task: CopyFiles@2
        displayName: 'Prepare Artifacts'
        inputs:
          Contents: |
            $(StructureFilePath)
            build-summary.md
            eslint-results.json
            $(Build.ArtifactStagingDirectory)/test-results/**
          TargetFolder: '$(Build.ArtifactStagingDirectory)/reports'
          flattenFolders: false

      - task: PublishMarkdownReports@1
        displayName: 'Publish Analysis Reports'
        inputs:
          contentPath: '$(Build.ArtifactStagingDirectory)/reports'
          indexFile: '$(StructureFileName)'
          latexFormula: true
        condition: succeeded()

      - task: PublishBuildArtifacts@1
        displayName: 'Publish Artifacts'
        inputs:
          PathtoPublish: '$(Build.ArtifactStagingDirectory)'
          ArtifactName: 'introspection-identity-$(Build.BuildNumber)'