{"description": "Shared mongoose models, enums and schemas for microservices.", "name": "excelytics.shared-models", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "version": "0.3.21", "type": "module", "author": "<PERSON><PERSON>", "license": "UNLICENSED", "scripts": {"clean": "rm -rf dist", "build": "bun run clean && tsc", "prepublishOnly": "bun run build", "publish:package": "bun install && bun run build && bun publish", "deprecated:script:info": "bun run src/scripts/deprecated_cli.ts", "map:deprecated:all": "bun run src/scripts/deprecated_cli.ts script map . --show-all-with-hide-list", "map:deprecated:simple": "bun run src/scripts/deprecated_cli.ts map . --show-all-with-hide-list", "map:deprecated:sonnet": "bun run src/scripts/deprecated_cli.ts map:sonnet . --show-all-with-hide-list", "test:config": "bun run tests/test-config-internal.ts", "test:config:cli": "bun run src/scripts/cli.ts config:test", "test:system": "bun run tests/test-refactored-system.ts", "test:cli-arch": "bun run tests/test-cli-architecture.ts", "reinstall": "bun run src/scripts/cli.ts reinstall", "clean:imports": "bun run src/scripts/cli.ts clean:imports", "script:info": "bun run src/scripts/cli.ts config:test", "map:simple": "bun run src/scripts/cli.ts map . --show-all-with-hide-list", "map:enhanced": "bun run src/scripts/cli.ts map:enhanced --show-all-with-hide-list"}, "exports": {".": "./dist/index.js", "./config": "./dist/config/index.js", "./constants": "./dist/constants/index.js", "./helpers": "./dist/helpers/index.js", "./models": "./dist/models/index.js", "./schemas": "./dist/schemas/index.js", "./scripts": "./dist/scripts/index.js", "./types": "./dist/types/index.js"}, "keywords": ["constants", "schemas", "scripts", "models", "enums", "types", "config"], "files": ["dist", "src"], "devDependencies": {"@types/bun": "latest", "@types/mongoose": "^5.11.97", "bun": "^1.2.15", "bun-types": "^1.2.15"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@typegoose/typegoose": "^12.10.1", "cosmiconfig": "^9.0.0", "deepmerge": "^4.3.1", "mongoose": "~8.15.1", "zod": "^3.25.56"}, "engines": {"bun": ">=1.0.0"}, "publishConfig": {"registry": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/"}}