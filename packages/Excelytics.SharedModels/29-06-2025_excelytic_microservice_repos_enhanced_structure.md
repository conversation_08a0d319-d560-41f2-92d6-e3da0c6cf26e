# 📊 Project Analysis: excelytic_microservice_repos

## 📋 Summary
- **Generated:** (Sun) 29-06-2025 06:06pm SAST
- **Analysis Duration:** 10ms
- **Root Path:** `/Users/<USER>/dev_code_directory/Introspection/excelytic_microservice_repos`

## 🔍 Git Information
Git information not available

## 📦 Package Information  
Package.json not found or invalid

## 📈 Project Metrics

### 📊 Overview
- **Total Files:** 683
- **Total Directories:** 223
- **Total Size:** 31.6 MB
- **Lines of Code:** 50,252

### 📋 File Distribution
| Extension | Files | Lines of Code |
|-----------|-------|---------------|
| .ts | 417 | 27,622 |
| .md | 72 | 19,100 |
| .xml | 56 | 0 |
| no ext | 32 | 0 |
| .json | 23 | 1,116 |
| .tsx | 16 | 713 |
| .txt | 14 | 0 |
| .yaml | 12 | 1,013 |
| .yml | 7 | 351 |
| .mjs | 5 | 0 |
| .iml | 5 | 0 |
| .js | 5 | 317 |
| .log | 3 | 0 |
| .patch | 3 | 0 |
| .development | 3 | 0 |

### 📂 Largest Files
| File | Size | Path |
|------|------|------|
| AugmentWebviewStateStore.xml | 19.4 MB | ../../../Introspection.Identity/.idea/AugmentWebviewStateStore.xml |
| AugmentWebviewStateStore.xml | 3.8 MB | ../../.idea/AugmentWebviewStateStore.xml |
| Instrospection ERD Schema v2.png | 2.2 MB | ../../../Introspection.Finance/docs/old-docs/Instrospection ERD Schema v2.png |
| Instrospection ERD Schema v1.png | 1.8 MB | ../../../Introspection.Finance/docs/old-docs/Instrospection ERD Schema v1.png |
| AugmentWebviewStateStore.xml | 1.1 MB | ../../../Introspection.Finance/.idea/AugmentWebviewStateStore.xml |
| access.log | 618.4 KB | ../../../Introspection.Finance/logs/access.log |
| access.log | 377.0 KB | ../../../Introspection.Identity/logs/access.log |
| bun.lockb | 181.5 KB | ../../../Introspection.Finance/.idea/shelf/Uncommitted_changes_before_Checkout_at_22_12_2024,_3_57 pm_[Changes]/bun.lockb |
| new-api-context.txt | 70.2 KB | ../../../Introspection.Finance/docs/contexts/new-api-context.txt |
| context-for-ai-models.md | 40.2 KB | ../../documentation/context-for-ai-models.md |

### 🕐 Recently Modified Files
| File | Modified | Path |
|------|----------|------|
| package.json | 29-06-2025 | ../../../Introspection.Identity/package.json |
| workspace.xml | 29-06-2025 | ../../../Introspection.Identity/.idea/workspace.xml |
| .excelyticsrc.json | 29-06-2025 | ../../../Introspection.Identity/.excelyticsrc.json |
| cli_v2.ts | 29-06-2025 | ../../../Introspection.Identity/scripts/cli_v2.ts |
| package.json | 29-06-2025 | package.json |

## 🌳 Directory Structure
```
excelytic_microservice_repos/
├── Introspection.Finance.Calc/
│   ├── types/
│   │   ├── env_calc.ts
│   │   ├── express.d.ts
│   │   ├── excel.types.ts
│   │   └── global.d.ts
│   ├── tests/
│   │   └── excel/
│   │       ├── excel.tests.ts
│   │       ├── temp-files/
│   │       └── excel.test-constants.ts
│   ├── uploads/
│   ├── pipelines/
│   │   └── steps/
│   │       ├── build-and-test.yml
│   │       ├── setup-environment.yml
│   │       ├── install-dependencies.yml
│   │       └── analyze-structure.yml
│   ├── shared/
│   │   ├── calc_setup.md
│   │   └── header_information.md
│   ├── azure-pipelines.yaml
│   ├── logs/
│   │   └── access.log
│   ├── .gitignore
│   ├── package.json
│   ├── .env
│   ├── .prettierrc
│   ├── eslint.config.mjs
│   └── src/
│       ├── middleware/
│       │   ├── security-options.middleware.ts
│       │   ├── index.ts
│       │   ├── custom.middleware.ts
│       │   └── rate-limit.middleware.ts
│       ├── config/
│       │   └── db.ts
│       ├── constants/
│       │   ├── excel.dropdown.constants.ts
│       │   ├── excel.template.constants.ts
│       │   ├── excel.color.constants.ts
│       │   └── excel.formulae.constants.ts
│       ├── cli.ts
│       ├── utils/
│       │   ├── excel.utility.ts
│       │   └── excel.validator.ts
│       ├── models/
│       │   ├── finance-raw.model.ts
│       │   ├── finance-category-total.model.ts
│       │   └── finance-category-summary.model.ts
│       ├── schemas/
│       │   ├── excel-data.schema.ts
│       │   ├── finance-raw.schema.ts
│       │   ├── finance-category-summary.schema.ts
│       │   └── finance-category-total.schema.ts
│       ├── controllers/
│       │   └── excel.controller.ts
│       ├── server.ts
│       ├── routes/
│       │   ├── health.routes.ts
│       │   ├── excel.route.ts
│       │   └── index.ts
│       ├── helpers/
│       │   ├── excel-file-reader.helper.ts
│       │   └── excel-template.helper.ts
│       └── services/
│           ├── excel-template.service.ts
│           ├── company-directory.service.ts
│           ├── excel-lookup.service.ts
│           ├── excel-calculation-service.ts
│           └── excel-file-reader.service.ts
├── Introspection.Finance.Client/
│   ├── tailwind.config.js
│   ├── tsconfig.base.json
│   ├── tests/
│   ├── server/
│   │   ├── config/
│   │   │   ├── scripts/
│   │   │   │   └── cli.ts
│   │   │   ├── dev-server.ts
│   │   │   ├── server.ts
│   │   │   └── build.ts
│   │   ├── cli.ts
│   │   ├── env.js
│   │   ├── package.json
│   │   ├── .env
│   │   └── env.ts
│   ├── shared/
│   │   ├── bundling_notes.md
│   │   ├── 2025-06-10_Introspection.Finance.Client_structure.md
│   │   ├── client-context.txt
│   │   ├── 2025-06-09_Introspection.Finance.Client_structure.md
│   │   ├── client_setup.md
│   │   └── 2025-06-17_Introspection.Finance.Client_structure.md
│   ├── azure-pipelines.yml
│   ├── .gitignore
│   ├── package.json
│   ├── .env
│   ├── .prettierrc
│   ├── postcss.config.js
│   ├── client/
│   │   ├── types/
│   │   │   ├── express.d.ts
│   │   │   ├── env.types.ts
│   │   │   ├── auth.types.ts
│   │   │   └── global.d.ts
│   │   ├── config/
│   │   │   └── env_client.ts
│   │   ├── public/
│   │   │   └── index.html
│   │   ├── package.json
│   │   └── src/
│   │       ├── app.tsx
│   │       ├── main.tsx
│   │       ├── contexts/
│   │       │   └── AuthContext.tsx
│   │       ├── constants/
│   │       │   └── uri.constants.ts
│   │       ├── index.css
│   │       ├── components/
│   │       │   ├── ui/
│   │       │   │   └── button.tsx
│   │       │   ├── layout/
│   │       │   │   ├── MainLayout.tsx
│   │       │   │   ├── Footer.tsx
│   │       │   │   └── Header.tsx
│   │       │   ├── FileUpload.tsx
│   │       │   └── FileUploadSonnet.tsx
│   │       ├── hooks/
│   │       │   └── useAuth.ts
│   │       ├── lib/
│   │       │   ├── utils.ts
│   │       │   └── apiClient.ts
│   │       ├── api/
│   │       │   ├── calc.ts
│   │       │   ├── finance.ts
│   │       │   └── auth.ts
│   │       ├── assets/
│   │       │   └── react.svg
│   │       ├── pages/
│   │       │   ├── auth/
│   │       │   │   ├── RegisterPage.tsx
│   │       │   │   └── LoginPage.tsx
│   │       │   ├── NotFoundPage.tsx
│   │       │   ├── DashboardPage.tsx
│   │       │   └── HomePage.tsx
│   │       └── routes/
│   │           ├── AppRoutes.tsx
│   │           └── ProtectedRoutes.tsx
│   └── eslint.config.mjs
├── Introspection.Finance/
│   ├── types/
│   │   ├── db.types.ts
│   │   ├── odata.types.ts
│   │   ├── env_.ts
│   │   └── express.d.ts
│   ├── tests/
│   │   ├── middleware/
│   │   │   └── rateLimit.test.ts
│   │   ├── generic-test.ts
│   │   ├── entity/
│   │   │   ├── enums/
│   │   │   │   ├── editions.test.ts
│   │   │   │   ├── user-type.test.ts
│   │   │   │   ├── account-type.test.ts
│   │   │   │   ├── category.test.ts
│   │   │   │   └── subcategory.test.ts
│   │   │   ├── internal/
│   │   │   │   ├── user.test.ts
│   │   │   │   └── organization.test.ts
│   │   │   ├── template.test.ts
│   │   │   ├── external/
│   │   │   │   ├── company-group.test.ts
│   │   │   │   ├── company.test.ts
│   │   │   │   ├── company-directory.test.ts
│   │   │   │   └── individual.test.ts
│   │   │   └── data/
│   │   │       ├── finance-raw.test.ts
│   │   │       ├── finance-category-summary.test.ts
│   │   │       └── finance-category-total.test.ts
│   │   ├── odata-generic-test.ts
│   │   └── config.ts
│   ├── bunfig.toml
│   ├── uploads/
│   │   └── DO_NOT_REMOVE.txt
│   ├── docs/
│   │   ├── contexts/
│   │   │   ├── new-api-context.txt
│   │   │   └── routes.txt
│   │   ├── file-structures/
│   │   │   ├── 2025-06-16_Introspection.Finance_structure.md
│   │   │   ├── 2025-06-13_Introspection.Finance_structure.md
│   │   │   └── 2025-06-15_Introspection.Finance_structure.md
│   │   ├── informational-notes/
│   │   │   ├── versioning.md
│   │   │   ├── service-changes.md
│   │   │   ├── latest_finance_objectives.md
│   │   │   ├── latest_refactor_15.06.2025.md
│   │   │   ├── import_vs_type_import.md
│   │   │   ├── tsconfig-types.md
│   │   │   ├── model-roles.md
│   │   │   └── never_vs_any_ts.md
│   │   ├── 2025-06-18_Introspection.Finance_structure_deep.md
│   │   └── old-docs/
│   │       ├── Instrospection ERD Schema v2.png
│   │       ├── Instrospection ERD Schema v1.png
│   │       ├── Microservices Architecture.md
│   │       ├── environment-variables.md
│   │       ├── Index.md
│   │       ├── Models.md
│   │       ├── System Overview.md
│   │       └── schema_v2.md
│   ├── azure-pipelines.yml
│   ├── logs/
│   │   └── access.log
│   ├── .gitignore
│   ├── package.json
│   ├── .env
│   ├── .prettierrc
│   ├── eslint.config.mjs
│   └── src/
│       ├── middleware/
│       │   ├── security-options.middleware.ts
│       │   ├── authentication.middleware.ts
│       │   ├── rate-limiter.middleware.ts
│       │   ├── index.ts
│       │   └── custom.middleware.ts
│       ├── core/
│       │   ├── common/
│       │   │   ├── repository.service.ts
│       │   │   ├── generic.controller.ts
│       │   │   ├── mutator.service.ts
│       │   │   ├── odata-metadata-service.ts
│       │   │   └── generic.service.ts
│       │   └── validators/
│       │       └── external/
│       │           └── company-directory.validator.ts
│       ├── shared/
│       │   ├── config/
│       │   │   └── database.ts
│       │   ├── constants/
│       │   │   ├── uri.constants.ts
│       │   │   └── localizations/
│       │   │       └── messages.ts
│       │   ├── scripts/
│       │   │   ├── cli.ts
│       │   │   └── maintenance/
│       │   │       ├── drop-tables.ts
│       │   │       ├── populate/
│       │   │       │   ├── populate-tables.ts
│       │   │       │   ├── internal/
│       │   │       │   │   ├── organization.populate.ts
│       │   │       │   │   ├── user.populate.ts
│       │   │       │   │   └── index.ts
│       │   │       │   └── external/
│       │   │       │       ├── company-group.populate.ts
│       │   │       │       ├── company-directory.populate.ts
│       │   │       │       ├── company.populate.ts
│       │   │       │       ├── index.ts
│       │   │       │       └── individual.populate.ts
│       │   │       └── seeds/
│       │   │           ├── seed-enums.ts
│       │   │           └── enums/
│       │   │               ├── enum-subcategory.savings.seed.ts
│       │   │               ├── enum-subcategory.income.seed.ts
│       │   │               ├── enum-subcategory.expenses.seed.ts
│       │   │               ├── enum-user-type.seed.ts
│       │   │               ├── index.ts
│       │   │               ├── enum-category.seed.ts
│       │   │               ├── enum-account-type.seed.ts
│       │   │               └── enum-edition.seed.ts
│       │   ├── helpers/
│       │   │   ├── odata.helper.ts
│       │   │   ├── authentication.helper.ts
│       │   │   └── error.helper.ts
│       │   └── interfaces/
│       │       └── service.interface.ts
│       ├── models/
│       │   ├── internal/
│       │   │   ├── user.model.ts
│       │   │   ├── organization-access.model.ts
│       │   │   ├── organization.model.ts
│       │   │   ├── audit-log.model.ts
│       │   │   └── index.ts
│       │   └── data/
│       │       ├── finance-raw.model.ts
│       │       ├── finance-category-total.model.ts
│       │       ├── index.ts
│       │       └── finance-category-summary.model.ts
│       ├── schemas/
│       │   ├── enums/
│       │   │   └── enum-category.schema.ts
│       │   ├── internal/
│       │   │   ├── audit-trail.schema.ts
│       │   │   ├── user-validation.schema.ts
│       │   │   └── organization.schema.ts
│       │   └── data/
│       │       ├── finance-raw.schema.ts
│       │       ├── finance-category-summary.schema.ts
│       │       └── finance-category-total.schema.ts
│       ├── controllers/
│       │   ├── enums/
│       │   │   ├── enum-edition.controller.ts
│       │   │   ├── enum-account-type.controller.ts
│       │   │   ├── enum-user-type.controller.ts
│       │   │   ├── enum-category.controller.ts
│       │   │   └── enum-subcategory.controller.ts
│       │   ├── internal/
│       │   │   ├── user.controller.ts
│       │   │   ├── organization.controller.ts
│       │   │   └── audit-log.controller.ts
│       │   ├── external/
│       │   │   ├── company.controller.ts
│       │   │   ├── company-directory.controller.ts
│       │   │   ├── individual.controller.ts
│       │   │   └── company-group.controller.ts
│       │   └── data/
│       │       ├── finance-category-summary.controller.ts
│       │       ├── finance-raw.controller.ts
│       │       └── finance-category-total.controller.ts
│       ├── server.ts
│       ├── routes/
│       │   ├── enums/
│       │   │   ├── enum-account-type.route.ts
│       │   │   ├── enum-category.route.ts
│       │   │   ├── enum-edition.route.ts
│       │   │   ├── enum-subcategory.route.ts
│       │   │   ├── enum-user-type.route.ts
│       │   │   └── index.ts
│       │   ├── internal/
│       │   │   ├── audit-log.route.ts
│       │   │   ├── index.ts
│       │   │   ├── user.route.ts
│       │   │   └── organization.route.ts
│       │   ├── health.routes.ts
│       │   ├── base.routes.ts
│       │   ├── index.ts
│       │   ├── external/
│       │   │   ├── company-directory.route.ts
│       │   │   ├── individual.route.ts
│       │   │   ├── index.ts
│       │   │   ├── company.route.ts
│       │   │   └── company-group.route.ts
│       │   └── data/
│       │       ├── finance-category-summary.route.ts
│       │       ├── finance-category-total.route.ts
│       │       ├── finance-raw.route.ts
│       │       └── index.ts
│       └── services/
│           ├── repositories/
│           │   ├── enums/
│           │   │   ├── enum-subcategory.repository.ts
│           │   │   ├── enum-category.repository.ts
│           │   │   ├── enum-user-type.repository.ts
│           │   │   ├── enum-account-type.repository.ts
│           │   │   └── enum-edition.repository.ts
│           │   ├── internal/
│           │   │   ├── audit-log.repository.ts
│           │   │   ├── organization.repository.ts
│           │   │   └── user.repository.ts
│           │   ├── external/
│           │   │   ├── company-group.repository.ts
│           │   │   ├── individual.repository.ts
│           │   │   ├── company-directory.repository.ts
│           │   │   └── company.repository.ts
│           │   └── data/
│           │       ├── finance-category-total.repository.ts
│           │       ├── finance-raw.repository.ts
│           │       └── finance-category-summary.repository.ts
│           └── mutators/
│               ├── enums/
│               │   ├── enum-edition.service.ts
│               │   ├── enum-account-type.service.ts
│               │   ├── enum-user-type.service.ts
│               │   ├── enum-subcategory.service.ts
│               │   └── enum-category.service.ts
│               ├── internal/
│               │   ├── organization.service.ts
│               │   ├── audit-log.service.ts
│               │   └── user.service.ts
│               ├── external/
│               │   ├── individual.service.ts
│               │   ├── company-directory.service.ts
│               │   ├── company.service.ts
│               │   └── company-group.service.ts
│               └── data/
│                   ├── finance-raw.service.ts
│                   ├── finance-category-total.service.ts
│                   └── finance-category-summary.service.ts
├── Introspection.Identity/
│   ├── types/
│   │   ├── env_.ts
│   │   ├── express.d.ts
│   │   ├── global.d.ts
│   │   └── service.types.ts
│   ├── tests/
│   │   ├── security.test.ts
│   │   ├── test-runner.ts
│   │   ├── token-expiration.test.ts
│   │   ├── constants/
│   │   │   ├── test-config.constants.ts
│   │   │   ├── test-user.constants.ts
│   │   │   └── test.types.ts
│   │   ├── token-management.test.ts
│   │   ├── session.test.ts
│   │   ├── integration.test.ts
│   │   ├── identity-management.test.ts
│   │   ├── helpers/
│   │   │   ├── test-logger.ts
│   │   │   ├── token-analysis-helper.ts
│   │   │   └── auth.test-helper.ts
│   │   ├── authentication.test.ts
│   │   ├── health.test.ts
│   │   ├── session-middleware.test.ts
│   │   └── deprecated/
│   │       └── middleware.test.ts
│   ├── pipelines/
│   │   └── steps/
│   │       ├── setup-environment.yaml
│   │       ├── analyze-structure.yaml
│   │       └── install-dependencies.yaml
│   ├── shared/
│   │   ├── idp-summary.md
│   │   ├── microservices-architecture/
│   │   │   ├── token-types.md
│   │   │   └── token-payload-types.md
│   │   ├── future-enhancements/
│   │   │   └── enhancements.txt
│   │   ├── jwt_issuer.md
│   │   ├── private-module-creation.md
│   │   ├── notes/
│   │   │   ├── testing-knowledge.txt
│   │   │   ├── tenant-implementation.txt
│   │   │   ├── comprehensive-config-docs-gemini2.5flash.md
│   │   │   ├── planning/
│   │   │   │   ├── 2.what-is-identity-provider.txt
│   │   │   │   ├── 1.folder-structure.txt
│   │   │   │   ├── 3.how-IdP-is-used.txt
│   │   │   │   └── 4.authentication-access-process.txt
│   │   │   ├── zod-knowledge.txt
│   │   │   ├── api-security-knowledge.txt
│   │   │   └── tenant-knowledge.txt
│   │   ├── api-docs/
│   │   │   ├── environment-variables.md
│   │   │   └── API-Endpoints.md
│   │   └── session_information.md
│   ├── docs/
│   │   ├── pipeline-refactor-summary.md
│   │   ├── config/
│   │   │   ├── prettier-configuration.md
│   │   │   └── method-wrapping-guide.md
│   │   ├── obsolete-test-methods.md
│   │   ├── notes/
│   │   │   ├── scripts-to-try.md
│   │   │   ├── mongodb-session-store.md
│   │   │   ├── error-handling-improvements.md
│   │   │   └── token-analysis-summary.md
│   │   ├── scripts/
│   │   │   ├── test-scripts-quick-reference.md
│   │   │   └── package-scripts-documentation.md
│   │   ├── Cross-Service-User-Management.md
│   │   ├── Rate-Limiting-Strategy.md
│   │   └── latest-docs/
│   │       ├── Identity-Model-Security-Architecture.md
│   │       ├── Improved-Finance-User-Model.md
│   │       ├── API-Documentation.md
│   │       ├── Identity-Finance-Models-Analysis.md
│   │       ├── Microservices-User-Management-Recommendations.md
│   │       ├── Updated_IdP-API-Documentation.md
│   │       └── CORS-Configuration.md
│   ├── .prettierignore
│   ├── .editorconfig
│   ├── azure-pipelines.yml
│   ├── logs/
│   │   └── access.log
│   ├── .gitignore
│   ├── package.json
│   ├── .env
│   ├── scripts/
│   │   ├── preserve-comments.js
│   │   └── cli_v2.ts
│   ├── .prettierrc
│   ├── eslint.config.mjs
│   ├── .excelyticsrc.json
│   └── src/
│       ├── middleware/
│       │   ├── security/
│       │   │   ├── cookie-security.middleware.ts
│       │   │   └── security-options.middleware.ts
│       │   ├── utility.middleware.ts
│       │   ├── index.ts
│       │   ├── custom.middleware.ts
│       │   ├── authentication/
│       │   │   ├── authentication.middleware.ts
│       │   │   └── validation.middleware.ts
│       │   └── rate-limit.middleware.ts
│       ├── config/
│       │   ├── init-console.logger.ts
│       │   ├── scripts/
│       │   │   └── cli.ts
│       │   ├── log-level.config.ts
│       │   └── db.ts
│       ├── constants/
│       │   └── uri.constants.ts
│       ├── utils/
│       │   ├── password.utils.ts
│       │   ├── token-analysis.utils.ts
│       │   └── validation.util.ts
│       ├── models/
│       │   ├── refresh-token.model.ts
│       │   └── identity.model.ts
│       ├── schemas/
│       │   └── user.schema.ts
│       ├── examples/
│       │   └── token-usage-examples.ts
│       ├── controllers/
│       │   ├── authentication.controller.ts
│       │   └── identity.controller.ts
│       ├── server.ts
│       ├── routes/
│       │   ├── identity.routes.ts
│       │   ├── utility.routes.ts
│       │   ├── health.routes.ts
│       │   ├── index.ts
│       │   └── authentication.routes.ts
│       ├── helpers/
│       │   └── authentication.helper.ts
│       ├── services/
│       │   ├── authentication.service.ts
│       │   ├── jwt.service.ts
│       │   ├── health.service.ts
│       │   └── identity.service.ts
│       └── interfaces/
│           └── auth.interface.ts
└── Introspection.Shared/
    ├── documentation/
    │   ├── pipeline-updates-summary.md
    │   ├── config-discovery-refactor.md
    │   ├── latest-refactor-notes/
    │   │   ├── ANALYSIS_AND_IMPROVEMENTS.md
    │   │   ├── ERROR_TYPES_AND_RESPONDERS.md
    │   │   ├── GLOBAL_ERROR_HANDLER_DOCUMENTATION.md
    │   │   ├── ERROR_HANDLING_ARCHITECTURE_DIAGRAMS.md
    │   │   └── ERROR_HANDLING_GUIDE.md
    │   ├── sonnet-solution-for-utilty-repo.md
    │   ├── miscellaneous/
    │   │   ├── d.ts-files.md
    │   │   ├── mongoose_information.md
    │   │   ├── bundling_notes.md
    │   │   ├── improvements.md
    │   │   ├── js_docs.md
    │   │   └── objectId_schema.md
    │   └── context-for-ai-models.md
    ├── .changeset/
    │   ├── config.json
    │   └── README.md
    ├── pipelines/
    │   └── steps/
    │       ├── run-tests.yaml
    │       ├── build-and-test.yaml
    │       ├── check-versions.yaml
    │       ├── publish-packages.yaml
    │       ├── setup-environment.yaml
    │       ├── analyze-structure.yaml
    │       └── install-dependencies.yaml
    ├── docs/
    │   └── TESTING_INTEGRATION.md
    ├── example-client-config.js
    ├── azure-pipelines.yaml
    ├── .gitignore
    ├── package.json
    ├── packages/
    │   ├── Excelytics.SharedModels/
    │   │   ├── tests/
    │   │   │   ├── test-refactored-system.ts
    │   │   │   ├── docs/
    │   │   │   │   ├── REFACTOR_SUMMARY.md
    │   │   │   │   └── ARCHITECTURE_IMPROVEMENTS.md
    │   │   │   ├── test-cli-architecture.ts
    │   │   │   └── test-config-internal.ts
    │   │   ├── .gitignore
    │   │   ├── package.json
    │   │   ├── .excelyticsrc.json
    │   │   └── src/
    │   │       ├── types/
    │   │       │   ├── script-config.types.ts
    │   │       │   ├── company-directory.types.ts
    │   │       │   ├── script.types.ts
    │   │       │   └── index.ts
    │   │       ├── config/
    │   │       │   ├── manager.ts
    │   │       │   ├── defaults.ts
    │   │       │   └── index.ts
    │   │       ├── constants/
    │   │       │   ├── enums/
    │   │       │   │   ├── audit-action.enum.ts
    │   │       │   │   ├── edition.enum.ts
    │   │       │   │   ├── category.enum.ts
    │   │       │   │   ├── client.enum.ts
    │   │       │   │   ├── user.enum.ts
    │   │       │   │   └── index.ts
    │   │       │   └── index.ts
    │   │       ├── models/
    │   │       │   ├── enums/
    │   │       │   │   ├── enum-user-type.model.ts
    │   │       │   │   ├── enum-edition.model.ts
    │   │       │   │   ├── index.ts
    │   │       │   │   ├── enum-category.model.ts
    │   │       │   │   ├── enum-account-type.model.ts
    │   │       │   │   └── enum-subcategory.model.ts
    │   │       │   ├── index.ts
    │   │       │   └── company-entities/
    │   │       │       ├── company.model.ts
    │   │       │       ├── company-group.model.ts
    │   │       │       ├── individual.model.ts
    │   │       │       ├── index.ts
    │   │       │       └── company-directory.model.ts
    │   │       ├── schemas/
    │   │       │   ├── utility.schema.ts
    │   │       │   ├── index.ts
    │   │       │   ├── authentication/
    │   │       │   │   ├── index.ts
    │   │       │   │   └── authentication.schema.ts
    │   │       │   └── company/
    │   │       │       ├── company-directory.input.schema.ts
    │   │       │       ├── company-directory.response.schema.ts
    │   │       │       ├── index.ts
    │   │       │       └── company.schema.ts
    │   │       ├── scripts/
    │   │       │   ├── package-reinstall.ts
    │   │       │   ├── cli.ts
    │   │       │   ├── import-cleanup.ts
    │   │       │   ├── index.ts
    │   │       │   └── deprecated/
    │   │       │       ├── deprecated_cli.ts
    │   │       │       ├── file-structure-creator.ts
    │   │       │       └── file-structure-creator-sonnet.ts
    │   │       ├── index.ts
    │   │       └── helpers/
    │   │           ├── metrics-analysis.helper.ts
    │   │           ├── tree-generation.helper.ts
    │   │           ├── file-structure.helper.ts
    │   │           ├── deprecated-script.helper.ts
    │   │           ├── enhanced-markdown.helper.ts
    │   │           └── list-config.helper.ts
    │   ├── Excelytics.SharedInternals/
    │   │   ├── .env.test
    │   │   ├── .gitignore
    │   │   ├── package.json
    │   │   ├── .env
    │   │   ├── tsup.config.ts
    │   │   ├── scripts/
    │   │   │   └── run-tests.sh
    │   │   └── src/
    │   │       ├── middleware/
    │   │       │   ├── error-handler.middleware.ts
    │   │       │   ├── logger.middleware.ts
    │   │       │   ├── health-check.middleware.ts
    │   │       │   ├── index.ts
    │   │       │   └── custom.middleware.ts
    │   │       ├── types/
    │   │       │   ├── auth/
    │   │       │   │   ├── token.types.ts
    │   │       │   │   ├── role.types.ts
    │   │       │   │   └── index.ts
    │   │       │   ├── metadata.types.ts
    │   │       │   ├── file.types.ts
    │   │       │   ├── calc/
    │   │       │   │   ├── calc.types.ts
    │   │       │   │   ├── chart.types.ts
    │   │       │   │   └── index.ts
    │   │       │   ├── database.types.ts
    │   │       │   ├── audit.types.ts
    │   │       │   ├── api/
    │   │       │   │   ├── response.types.ts
    │   │       │   │   ├── odata.types.ts
    │   │       │   │   ├── index.ts
    │   │       │   │   ├── service.types.ts
    │   │       │   │   └── health.types.ts
    │   │       │   ├── global.d.ts
    │   │       │   ├── index.ts
    │   │       │   └── error-logging.types.ts
    │   │       ├── config/
    │   │       │   ├── http.types.ts
    │   │       │   ├── services.config.ts
    │   │       │   └── index.ts
    │   │       ├── constants/
    │   │       │   ├── app.constants.ts
    │   │       │   ├── file.constants.ts
    │   │       │   ├── api.constants.ts
    │   │       │   ├── cache.constants.ts
    │   │       │   ├── process.constants.ts
    │   │       │   ├── permission-group.constants.ts
    │   │       │   ├── error.constants.ts
    │   │       │   ├── messages.constants.ts
    │   │       │   ├── index.ts
    │   │       │   └── auth.constants.ts
    │   │       ├── enums/
    │   │       │   ├── api.enum.ts
    │   │       │   ├── client.enum.ts
    │   │       │   ├── permission.enum.ts
    │   │       │   ├── auth.enum.ts
    │   │       │   ├── app.enum.ts
    │   │       │   ├── index.ts
    │   │       │   └── file.enum.ts
    │   │       ├── utils/
    │   │       │   ├── strings/
    │   │       │   │   ├── date.utils.ts
    │   │       │   │   ├── string.utils.ts
    │   │       │   │   └── index.ts
    │   │       │   ├── service-client.ts
    │   │       │   ├── env_internal.ts
    │   │       │   ├── auth/
    │   │       │   │   ├── index.ts
    │   │       │   │   ├── token.utils.ts
    │   │       │   │   └── permission.utils.ts
    │   │       │   ├── env_.ts
    │   │       │   ├── index.ts
    │   │       │   └── health-check.utils.ts
    │   │       ├── schemas/
    │   │       │   ├── utility.schema.ts
    │   │       │   ├── response.schema.ts
    │   │       │   ├── index.ts
    │   │       │   ├── auth.schema.ts
    │   │       │   └── token.schema.ts
    │   │       ├── __tests__/
    │   │       │   ├── error-helpers.test.ts
    │   │       │   ├── error-handler.test.ts
    │   │       │   ├── setup.ts
    │   │       │   └── integration.test.ts
    │   │       ├── index.ts
    │   │       ├── errors/
    │   │       │   ├── http.error.ts
    │   │       │   ├── validation.error.ts
    │   │       │   ├── mongoose.error.ts
    │   │       │   ├── service.error.ts
    │   │       │   ├── index.ts
    │   │       │   └── base.error.ts
    │   │       ├── helpers/
    │   │       │   ├── error-response.helper.ts
    │   │       │   ├── environment.helper.ts
    │   │       │   ├── health-check.helper.ts
    │   │       │   ├── health-check.factory.ts
    │   │       │   ├── token.helper.ts
    │   │       │   ├── error-logging.helper.ts
    │   │       │   ├── index.ts
    │   │       │   ├── error.helper.ts
    │   │       │   ├── message.helper.ts
    │   │       │   └── redis-cache.helper.ts
    │   │       ├── notifications/
    │   │       │   ├── api-success.notifications.ts
    │   │       │   ├── api-error.notifications.ts
    │   │       │   └── index.ts
    │   │       ├── services/
    │   │       │   ├── index.ts
    │   │       │   ├── redis.service.ts
    │   │       │   └── token.service.ts
    │   │       └── interfaces/
    │   │           ├── introspection.interface.ts
    │   │           ├── mongoose.interface.ts
    │   │           ├── index.ts
    │   │           └── service.interface.ts
    │   └── Excelytics.SharedDtos/
    │       ├── .gitignore
    │       ├── package.json
    │       └── src/
    │           ├── types/
    │           │   ├── response.types.ts
    │           │   ├── token.types.ts
    │           │   └── index.ts
    │           ├── auth/
    │           │   ├── index.ts
    │           │   └── authentication.schema.ts
    │           ├── enums/
    │           │   ├── api.enum.ts
    │           │   ├── client.enum.ts
    │           │   ├── app.enum.ts
    │           │   └── index.ts
    │           ├── utils/
    │           │   ├── env_.ts
    │           │   └── index.ts
    │           ├── scripts/
    │           │   ├── package-reinstall.ts
    │           │   ├── cli.ts
    │           │   ├── import-cleanup.ts
    │           │   ├── file-structure-creator.ts
    │           │   ├── file-structure-creator-sonnet.ts
    │           │   ├── script.types.ts
    │           │   └── index.ts
    │           └── index.ts
    ├── .prettierrc
    └── eslint.config.mjs

```

## 🏗️ Architecture Notes
This is part of the **Introspection Consulting** microservices architecture:
- **Backend (BE):** Introspection.Finance
- **Identity Provider (IdP):** Introspection.Finance.Identity  
- **Calculation Engine:** Introspection.Finance.Calc
- **Frontend (FE):** Introspection.Finance.Client

**Tech Stack:** MongoDB, TypeScript, Shadcn/ui, Bun
**Deployment:** Unraid Server with Nginx Gateway

---
*Generated by Enhanced Project Structure Analyzer*
