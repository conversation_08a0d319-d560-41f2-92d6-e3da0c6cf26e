import { RateLimitHelper } from '@/helpers/rate-limit.helper';
import { ErrorCodes } from 'excelytics.shared-internals';
import rateLimit from 'express-rate-limit';

// General API rate limiting
// Counts all requests to provide a baseline protection against general abuse
const GeneralLimiter = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	limit: RateLimitHelper.getGeneralRateLimit(),
	legacyHeaders: false,
	standardHeaders: 'draft-7', // Recommended standard for future compatibility

	// For a general limiter, counting all requests is usually preferred.
	skipSuccessfulRequests: false,
	message: {
		success: false,
		message: 'Too many failed attempts, please try again later.',
		error: {
			code: ErrorCodes.RATE_LIMIT_EXCEEDED,
			message: 'Rate limit exceeded'
		}
	}
});

// Authentication endpoints (stricter, skips successful attempts)
const AuthenticationLimiter = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	limit: RateLimitHelper.getAuthRateLimit(),
	legacyHeaders: false,
	standardHeaders: 'draft-7',
	skipSuccessfulRequests: true, // Only count failed login/registration attempts
	message: {
		success: false,
		message: 'Too many authentication attempts, please try again later.',
		error: {
			code: ErrorCodes.AUTH_RATE_LIMIT_EXCEEDED,
			message: 'Authentication rate limit exceeded'
		}
	}
});

// Password reset (very strict, counts all attempts)
const PasswordResetLimiter = rateLimit({
	windowMs: 60 * 60 * 1000, // 1 hour
	limit: RateLimitHelper.getPasswordResetRateLimit(),
	legacyHeaders: false,
	standardHeaders: 'draft-7',
	skipSuccessfulRequests: false, // Default is false, count all password reset attempts
	message: {
		success: false,
		message: 'Too many password reset attempts, please try again later.',
		error: {
			code: ErrorCodes.PASSWORD_RESET_RATE_LIMIT_EXCEEDED,
			message: 'Password reset rate limit exceeded'
		}
	}
});

export const RateLimiters = {
	GeneralLimiter,
	PasswordResetLimiter,
	AuthenticationLimiter
};