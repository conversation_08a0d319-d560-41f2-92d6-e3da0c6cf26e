import { type EnumPermissions, EnumClient<PERSON><PERSON><PERSON>, EnumClientPath, type UserRoles } from 'excelytics.shared-internals';
import type { Request } from 'express';

export interface AuthenticatedRequest extends Request {}

// This is what you'd typically attach to request.user
export interface UserPayload {
	userId: string;
	email: string;
	roles?: string[] | undefined;
	clientId?: string;
	clientOrigin?: EnumClientOrigin;
	clientPath?: EnumClientPath;
	isActive?: boolean;
}

// Create a type from the object's values: 'admin' | 'user' | 'guest'
export type UserRole = (typeof UserRoles)[keyof typeof UserRoles];

export type Permission = (typeof EnumPermissions)[keyof typeof EnumPermissions];