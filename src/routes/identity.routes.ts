import { validateRequestParams, validationSchema } from '@/middleware/authentication/validation.middleware';
import { requireAdminRole, requireAuth } from '@/middleware/authentication/authentication.middleware';
import { IdentityController } from '@/controllers/identity.controller';
import asyncHand<PERSON> from 'express-async-handler';
import { Router } from 'express';

const router = Router();
const identityController = new IdentityController();

// --- SECURITY MIDDLEWARE ---
// All routes require authentication
router.use(asyncHandler(requireAuth));

//! --- USER ROUTES (Authenticated users can access their own data) ---
// Get current user's profile
router.get('/me', asyncHandler(identityController.getCurrentUser.bind(identityController)));

// Get user by email (users can access their own data, admins can access any)
router.get('/my/email/:email', asyncHandler(identityController.getUserByEmail.bind(identityController)));

// Update current user's profile
router.put('/me', asyncHandler(identityController.updateCurrentUser.bind(identityController)));

// Change current user's password
router.put('/me/password', asyncHandler(identityController.changePassword.bind(identityController)));

//! --- ADMIN ROUTES (Admin role required) ---
// Apply admin role requirement to all routes below this point
router.use(asyncHandler(requireAdminRole));

// Get all users (admin only)
router.get('/users', asyncHandler(identityController.getUsers.bind(identityController)));

// Get user by email (admin only) - matches test expectations
router.get(
	'/email/:email',
	validateRequestParams(validationSchema.emailParam),
	asyncHandler(identityController.getUserByEmail.bind(identityController))
);

// Get user by email (admin only) - alternative route
router.get(
	'/user/:email',
	validateRequestParams(validationSchema.emailParam),
	asyncHandler(identityController.getUserByEmail.bind(identityController))
);

// Update user by email (admin only)
router.put(
	'/user/:email',
	validateRequestParams(validationSchema.emailParam),
	asyncHandler(identityController.updateUserByEmail.bind(identityController))
);

// Delete user by email (admin only)
router.delete(
	'/email/:email',
	validateRequestParams(validationSchema.emailParam),
	asyncHandler(identityController.deleteUserByEmail.bind(identityController))
);

// Delete user by clientId (admin only)
router.delete(
	'/clientId/:clientId',
	validateRequestParams(validationSchema.clientIdParam),
	asyncHandler(identityController.deleteUserByClientId.bind(identityController))
);

export default router;