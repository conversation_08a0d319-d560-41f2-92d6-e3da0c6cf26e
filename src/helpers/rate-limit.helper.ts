import { EnumEnv } from 'excelytics.shared-internals';
import { env_idp } from '@app-types/env_.ts';

/**
 * Get environment-specific rate limits for general API usage
 * Balances security with usability for different environments
 */
function getGeneralRateLimit(): number {
	switch (env_idp.ENV) {
		case EnumEnv.Development:
			return 1000; // High limit for development testing
		case EnumEnv.Test:
			return 1000; // High limit for automated testing
		case EnumEnv.UAT:
			return 500; // Moderate limit for user acceptance testing
		case EnumEnv.Staging:
			return 300; // Production-like but slightly higher for testing
		case EnumEnv.Production:
			return 200; // Conservative limit for production
		default:
			return 200; // Default to production settings
	}
}

/**
 * Get environment-specific rate limits for authentication endpoints
 * Stricter limits to prevent brute force attacks
 */
function getAuthRateLimit(): number {
	switch (env_idp.ENV) {
		case EnumEnv.Development:
			return 50; // Higher for development testing
		case EnumEnv.Test:
			return 100; // Very high for automated testing
		case EnumEnv.UAT:
			return 25; // Moderate for user testing
		case EnumEnv.Staging:
			return 15; // Close to production
		case EnumEnv.Production:
			return 10; // Strict for production security
		default:
			return 10; // Default to production settings
	}
}

/**
 * Get environment-specific rate limits for password reset
 * Very strict to prevent abuse of password reset functionality
 */
function getPasswordResetRateLimit(): number {
	switch (env_idp.ENV) {
		case EnumEnv.Development:
			return 20; // Higher for development testing
		case EnumEnv.Test:
			return 50; // High for automated testing
		case EnumEnv.UAT:
			return 10; // Moderate for user testing
		case EnumEnv.Staging:
			return 7; // Close to production
		case EnumEnv.Production:
			return 5; // Very strict for production
		default:
			return 5; // Default to production settings
	}
}

export const RateLimitHelper = {
	getGeneralRateLimit,
	getAuthRateLimit,
	getPasswordResetRateLimit
};