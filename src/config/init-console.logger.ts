import { LogLevelOverrideEnum, consoleMethodLevels, getLogLevelFromEnv } from '@/config/log-level.config';
import { env_idp } from '@app-types/env_';

// Store original console methods
const originalConsole = {
	trace: console.trace,
	debug: console.debug,
	info: console.info,
	warn: console.warn,
	error: console.error,
	log: console.log
};

// Get the desired log level from your environment variable
const configuredLogLevel: LogLevelOverrideEnum = getLogLevelFromEnv(env_idp.LOG_LEVEL);

// Function to initialize/patch console methods
export function initializeConsoleLogger(): void {
	console.log(
		`[Console <PERSON>er] Initializing console logging with level: ${LogLevelOverrideEnum[configuredLogLevel]}`
	);

	// Loop through each relevant console method
	for (const methodName in consoleMethodLevels) {
		if (Object.prototype.hasOwnProperty.call(consoleMethodLevels, methodName)) {
			const methodLevel = consoleMethodLevels[methodName];
			const originalMethod = (originalConsole as any)[methodName];

			// Override the console method
			(console as any)[methodName] = function (...args: any[]) {
				if (methodLevel <= configuredLogLevel) {
					// If the message's level is less than or equal to the configured level, log it
					originalMethod.apply(console, args);
				}
			};
		}
	}

	// Add a specific `console.assert` check if you wish, as it behaves differently
	const originalAssert = console.assert;
	console.assert = function (condition?: boolean, ...data: any[]): void {
		if (LogLevelOverrideEnum.ERROR <= configuredLogLevel) {
			// Treat assert failures as errors
			originalAssert.apply(console, [condition, ...data]);
		}
	};
}