import { EnumEnv } from 'excelytics.shared-internals';
import { env_idp } from '@app-types/env_';
import mongoose from 'mongoose';

let connectionString: string;
if (env_idp.VPN) {
	console.log('Using Unraid MongoDb Instance...');
	connectionString = env_idp.MONGODB_URI!;
} else {
	console.log('Using local MongoDb Instance...');
	connectionString = env_idp.MONGODB_URI_LOCAL!;
}

console.log(env_idp.ENV === EnumEnv.Test ? 'Test environment triggered' : `${env_idp.ENV} environment running...`);

export const connectDB = async () => {
	try {
		await mongoose.connect(connectionString);
		console.log(`-- Connected to MongoDB in ${env_idp.ENV} mode`);
	} catch (err) {
		console.error('-- Error connecting to MongoDB:', err);
		process.exit(1);
	}
};